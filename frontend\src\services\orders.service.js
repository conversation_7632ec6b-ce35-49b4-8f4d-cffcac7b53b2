export default class OrdersService {
    static async getOrders() {
        try {
            const response = await fetch("/orders.json")
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }
            const orders = await response.json()
            return orders
        } catch (error) {
            console.error('Ошибка при загрузке заказов:', error)
            throw error
        }
    }

    static async getOrdersByStatus(status) {
        try {
            const response = await fetch("/orders.json")
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }
            const orders = await response.json()
            return orders.filter(order => order.status === status)
        } catch (error) {
            console.error('Ошибка при загрузке заказов по статусу:', error)
            throw error
        }
    }

    static async getOrderById(id) {
        try {
            const response = await fetch("/orders.json")
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`)
            }
            const orders = await response.json()
            return orders.find(order => order.id === id)
        } catch (error) {
            console.error('Ошибка при загрузке заказа по ID:', error)
            throw error
        }
    }

    static async updateOrderStatus(orderId, newStatus) {
        try {
            // Имитация обновления статуса локально
            // В будущем здесь будет вызов к серверу

            // Имитируем задержку сети
            await new Promise(resolve => setTimeout(resolve, 500))

            // Имитируем успешное обновление
            return {
                id: orderId,
                status: newStatus,
                updatedAt: new Date().toISOString()
            }
        } catch (error) {
            console.error('Ошибка при обновлении статуса заказа:', error)
            throw error
        }
    }
}