import {
  User,
  Phone,
  Truck,
  MessageSquare,
  CreditCard,
  MapPin,
  Package
} from "lucide-react";
import getStatusConfig from "@/utils/statusConfig";
import { cn } from "@/lib/utils";

export default function OrderCard({ order }) {
  const statusConfig = getStatusConfig(order.status);
  const StatusIcon = statusConfig.icon;

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300 mb-8">
      {/* Заголовок с номером заказа и статусом */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">
            Заказ #{order.id}
          </h3>
          <div className={cn(
            "inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border",
            statusConfig.className
          )}>
            <StatusIcon className="w-4 h-4" />
            {statusConfig.label}
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Информация о клиенте */}
        <div className="space-y-4">
          <h4 className="text-base font-semibold text-gray-900 flex items-center gap-2">
            <User className="w-5 h-5 text-gray-600" />
            Информация о клиенте
          </h4>
          <div className="bg-gray-50 rounded-xl p-4 space-y-3">
            <div className="flex items-center gap-3">
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-gray-900 font-medium">{order.user.first_name}</span>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="w-4 h-4 text-gray-500" />
              <a
                href={`tel:${order.user.phone}`}
                className="text-blue-600 hover:text-blue-800 transition-colors"
              >
                {order.user.phone}
              </a>
            </div>
          </div>
        </div>

        {/* Детали доставки */}
        <div className="space-y-4">
          <h4 className="text-base font-semibold text-gray-900 flex items-center gap-2">
            <Truck className="w-5 h-5 text-gray-600" />
            Доставка
          </h4>
          <div className="bg-gray-50 rounded-xl p-4 space-y-3">
            <div className="flex items-center gap-3">
              <MapPin className="w-4 h-4 text-gray-500" />
              <span className="text-gray-900">{order.delivery_type}</span>
            </div>
            {order.address && (
              <div className="flex items-start gap-3">
                <MapPin className="w-4 h-4 text-gray-500 mt-0.5" />
                <span className="text-gray-700">{order.address}</span>
              </div>
            )}
            {order.comment && (
              <div className="flex items-start gap-3">
                <MessageSquare className="w-4 h-4 text-gray-500 mt-0.5" />
                <span className="text-gray-700 italic">"{order.comment}"</span>
              </div>
            )}
          </div>
        </div>

        {/* Способ оплаты */}
        <div className="space-y-4">
          <h4 className="text-base font-semibold text-gray-900 flex items-center gap-2">
            <CreditCard className="w-5 h-5 text-gray-600" />
            Способ оплаты
          </h4>
          <div className="bg-gray-50 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <CreditCard className="w-4 h-4 text-gray-500" />
              <span className="text-gray-900">{order.paymentsystem}</span>
            </div>
          </div>
        </div>

        {/* Список блюд */}
        <div className="space-y-4">
          <h4 className="text-base font-semibold text-gray-900 flex items-center gap-2">
            <Package className="w-5 h-5 text-gray-600" />
            Состав заказа
          </h4>
          <div className="space-y-3">
            {order.meals.map((meal) => (
              <div
                key={meal.id}
                className="bg-gray-50 rounded-lg p-3 flex items-center justify-between hover:bg-gray-100 transition-colors"
              >
                <div className="flex-1">
                  <h5 className="font-medium text-gray-900 mb-2">{meal.name}</h5>
                  <div className="flex items-center gap-3 text-sm">
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      {Math.floor(meal.quantity / parseInt(meal.portion))} шт
                    </span>
                    <span className="text-gray-600">
                      по {meal.portion} {meal.unit}
                    </span>
                    <span className="text-gray-500">
                      • {meal.quantity} {meal.unit}
                    </span>
                  </div>
                </div>
                <div className="text-right ml-4">
                  <div className="text-lg font-semibold text-gray-900">
                    {meal.amount} ₽
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Итоговая сумма */}
        <div className="border-t border-gray-200 pt-6">
          <div className="flex items-center justify-between">
            <span className="text-lg font-semibold text-gray-900">Итого к оплате:</span>
            <span className="text-2xl font-bold text-gray-900">{order.amount} ₽</span>
          </div>
        </div>
      </div>
    </div>
  );
}