{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Container.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\n\r\nexport default function Container({\r\n  children,\r\n  className,\r\n  size = \"default\",\r\n  as: Component = \"div\",\r\n  ...props\r\n}) {\r\n  const sizeVariants = {\r\n    sm: \"max-w-4xl\",\r\n    default: \"max-w-6xl\",\r\n    lg: \"max-w-7xl\",\r\n    full: \"max-w-none\"\r\n  };\r\n\r\n  return (\r\n    <Component\r\n      className={cn(\r\n        \"w-full mx-auto transition-all duration-300 ease-in-out\",\r\n\r\n        sizeVariants[size],\r\n\r\n        \"px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16\",\r\n\r\n        \"relative\",\r\n\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </Component>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,UAAU,EAChC,QAAQ,EACR,SAAS,EACT,OAAO,SAAS,EAChB,IAAI,YAAY,KAAK,EACrB,GAAG,OACJ;IACC,MAAM,eAAe;QACnB,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DAEA,YAAY,CAAC,KAAK,EAElB,0CAEA,YAEA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Section.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport Container from \"./Container\";\n\nexport default function Section({ \n  children, \n  className,\n  containerClassName,\n  containerSize = \"default\",\n  spacing = \"default\",\n  background = \"transparent\",\n  as: Component = \"section\",\n  ...props \n}) {\n  const spacingVariants = {\n    none: \"\",\n    sm: \"py-8 md:py-12\",\n    default: \"py-12 md:py-16 lg:py-20\",\n    lg: \"py-16 md:py-20 lg:py-24\",\n    xl: \"py-20 md:py-24 lg:py-32\"\n  };\n\n  const backgroundVariants = {\n    transparent: \"\",\n    white: \"bg-white\",\n    gray: \"bg-gray-50\",\n    primary: \"bg-primary/5\",\n    muted: \"bg-muted\"\n  };\n\n  return (\n    <Component\n      className={cn(\n        // Базовые стили секции\n        \"relative w-full\",\n        \n        // Отступы\n        spacingVariants[spacing],\n        \n        // Фон\n        backgroundVariants[background],\n        \n        className\n      )}\n      {...props}\n    >\n      <Container \n        size={containerSize}\n        className={containerClassName}\n      >\n        {children}\n      </Container>\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,EAC9B,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,SAAS,EACzB,UAAU,SAAS,EACnB,aAAa,aAAa,EAC1B,IAAI,YAAY,SAAS,EACzB,GAAG,OACJ;IACC,MAAM,kBAAkB;QACtB,MAAM;QACN,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBAAuB;QACvB,mBAEA,UAAU;QACV,eAAe,CAAC,QAAQ,EAExB,MAAM;QACN,kBAAkB,CAAC,WAAW,EAE9B;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,yIAAA,CAAA,UAAS;YACR,MAAM;YACN,WAAW;sBAEV;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Header.jsx"], "sourcesContent": ["import Link from 'next/link';\r\nimport Container from './Container';\r\n\r\nexport default function Header() {\r\n    return (\r\n        <header className=\"sticky top-0 z-50 bg-white/10 backdrop-blur-md\">\r\n            <Container className=\"py-4 relative z-10\">\r\n              <Link href=\"/\">\r\n                <h1 className=\"text-2xl font-bagel-fat-one text-gray-900\">\r\n                  VKUS\r\n                </h1>\r\n              </Link>\r\n            </Container>\r\n        </header>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACpB,qBACI,8OAAC;QAAO,WAAU;kBACd,cAAA,8OAAC,yIAAA,CAAA,UAAS;YAAC,WAAU;sBACnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,8OAAC;oBAAG,WAAU;8BAA4C;;;;;;;;;;;;;;;;;;;;;AAO1E", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/index.js"], "sourcesContent": ["export { default as Container } from './Container';\nexport { default as Section } from './Section';\nexport { default as Header } from './Header';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/constants/orderStatus.js"], "sourcesContent": ["/**\n * Enum для статусов заказов\n * Содержит все возможные статусы заказов и их строковые значения\n */\nexport const ORDER_STATUS = {\n  NEW: 'new',\n  CONFIRMED: 'confirmed',\n  COMPLETED: 'completed',\n  GIVEN: 'given',        // Отдан (только для самовывоза)\n  DELIVERY: 'delivery',   // В пути (только для доставки)\n  DELIVERED: 'delivered', // Доставлен (только для доставки)\n  CANCELLED: 'cancelled'\n};\n\n/**\n * Массив статусов в порядке их следования в жизненном цикле заказа\n * Базовая цепочка для всех заказов\n */\nexport const STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.DELIVERY,\n  ORDER_STATUS.CANCELLED\n];\n\n/**\n * Цепочка статусов для самовывоза\n */\nexport const PICKUP_STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.GIVEN\n];\n\n/**\n * Цепочка статусов для доставки\n */\nexport const DELIVERY_STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.DELIVERY,\n  ORDER_STATUS.DELIVERED\n];\n\n/**\n * Статусы, которые не отображаются в интерфейсе (финальные)\n */\nexport const HIDDEN_STATUSES = [\n  ORDER_STATUS.GIVEN,\n  ORDER_STATUS.DELIVERED\n];\n\n/**\n * Проверяет, является ли переданное значение валидным статусом заказа\n * @param {string} status - Статус для проверки\n * @returns {boolean} - true если статус валидный\n */\nexport const isValidStatus = (status) => {\n  return Object.values(ORDER_STATUS).includes(status);\n};\n\n/**\n * Получает все возможные статусы заказов\n * @returns {string[]} - Массив всех статусов\n */\nexport const getAllStatuses = () => {\n  return Object.values(ORDER_STATUS);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AACM,MAAM,eAAe;IAC1B,KAAK;IACL,WAAW;IACX,WAAW;IACX,OAAO;IACP,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAMO,MAAM,eAAe;IAC1B,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,QAAQ;IACrB,aAAa,SAAS;CACvB;AAKM,MAAM,sBAAsB;IACjC,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,KAAK;CACnB;AAKM,MAAM,wBAAwB;IACnC,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,QAAQ;IACrB,aAAa,SAAS;CACvB;AAKM,MAAM,kBAAkB;IAC7B,aAAa,KAAK;IAClB,aAAa,SAAS;CACvB;AAOM,MAAM,gBAAgB,CAAC;IAC5B,OAAO,OAAO,MAAM,CAAC,cAAc,QAAQ,CAAC;AAC9C;AAMO,MAAM,iBAAiB;IAC5B,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/statusConfig.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>t, CheckCircle, Truck, Package, Clock, HandHeart, CheckChe<PERSON> } from \"lucide-react\";\r\nimport { ORDER_STATUS } from '@/constants/orderStatus';\r\n\r\nexport default function getStatusConfig(status) {\r\n    const configs = {\r\n        [ORDER_STATUS.NEW]: {\r\n            icon: Clock,\r\n            label: \"Новый\",\r\n            buttonLabel: \"Подтвердить\",\r\n            className: \"bg-orange-50 text-orange-600 border-orange-200\"\r\n        },\r\n        [ORDER_STATUS.CONFIRMED]: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвержден\",\r\n            buttonLabel: \"Готов\",\r\n            className: \"bg-blue-50 text-blue-600 border-blue-200\"\r\n        },\r\n        [ORDER_STATUS.COMPLETED]: {\r\n            icon: Package,\r\n            label: \"Готов\",\r\n            buttonLabel: \"Отдать\",\r\n            className: \"bg-green-50 text-green-600 border-green-200\"\r\n        },\r\n        [ORDER_STATUS.GIVEN]: {\r\n            icon: HandHeart,\r\n            label: \"Отдан\",\r\n            buttonLabel: \"Отдан\",\r\n            className: \"bg-emerald-50 text-emerald-600 border-emerald-200\"\r\n        },\r\n        [ORDER_STATUS.DELIVERY]: {\r\n            icon: Truck,\r\n            label: \"В пути\",\r\n            buttonLabel: \"Доставлен\",\r\n            className: \"bg-purple-50 text-purple-600 border-purple-200\"\r\n        },\r\n        [ORDER_STATUS.DELIVERED]: {\r\n            icon: CheckCheck,\r\n            label: \"Доставлен\",\r\n            buttonLabel: \"Доставлен\",\r\n            className: \"bg-indigo-50 text-indigo-600 border-indigo-200\"\r\n        },\r\n        [ORDER_STATUS.CANCELLED]: {\r\n            icon: TriangleAlert,\r\n            label: \"Отменен\",\r\n            buttonLabel: \"Отменен\",\r\n            className: \"bg-red-50 text-red-600 border-red-200\"\r\n        }\r\n    };\r\n    return configs[status] || configs[ORDER_STATUS.NEW];\r\n}\r\n\r\n/**\r\n * Получает конфигурацию для кнопки перехода к следующему статусу\r\n * @param {string} nextStatus - Следующий статус\r\n * @returns {object} - Конфигурация кнопки\r\n */\r\nexport function getNextStatusButtonConfig(nextStatus) {\r\n    const buttonConfigs = {\r\n        [ORDER_STATUS.CONFIRMED]: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвердить\",\r\n            className: \"bg-blue-500 text-white hover:bg-blue-600\"\r\n        },\r\n        [ORDER_STATUS.COMPLETED]: {\r\n            icon: Package,\r\n            label: \"Готов\",\r\n            className: \"bg-green-500 text-white hover:bg-green-600\"\r\n        },\r\n        [ORDER_STATUS.GIVEN]: {\r\n            icon: HandHeart,\r\n            label: \"Отдать\",\r\n            className: \"bg-emerald-500 text-white hover:bg-emerald-600\"\r\n        },\r\n        [ORDER_STATUS.DELIVERY]: {\r\n            icon: Truck,\r\n            label: \"В доставку\",\r\n            className: \"bg-purple-500 text-white hover:bg-purple-600\"\r\n        },\r\n        [ORDER_STATUS.DELIVERED]: {\r\n            icon: CheckCheck,\r\n            label: \"Доставлен\",\r\n            className: \"bg-indigo-500 text-white hover:bg-indigo-600\"\r\n        }\r\n    };\r\n\r\n    return buttonConfigs[nextStatus] || {\r\n        icon: CheckCircle,\r\n        label: \"Далее\",\r\n        className: \"bg-gray-500 text-white hover:bg-gray-600\"\r\n    };\r\n}"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAEe,SAAS,gBAAgB,MAAM;IAC1C,MAAM,UAAU;QACZ,CAAC,+HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;YAChB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE;YAClB,MAAM,gNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,EAAE;YACrB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,WAAW;QACf;IACJ;IACA,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,+HAAA,CAAA,eAAY,CAAC,GAAG,CAAC;AACvD;AAOO,SAAS,0BAA0B,UAAU;IAChD,MAAM,gBAAgB;QAClB,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE;YAClB,MAAM,gNAAA,CAAA,YAAS;YACf,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,EAAE;YACrB,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,CAAC,+HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,WAAW;QACf;IACJ;IAEA,OAAO,aAAa,CAAC,WAAW,IAAI;QAChC,MAAM,2NAAA,CAAA,cAAW;QACjB,OAAO;QACP,WAAW;IACf;AACJ", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/shared/OrderReviewCard.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport Link from \"next/link\"\r\nimport getStatusConfig from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function OrderReviewCard({ order }) {\r\n    const statusConfig = getStatusConfig(order.status)\r\n    const StatusIcon = statusConfig.icon\r\n\r\n    return (\r\n        <div className=\"bg-gray-50 rounded-xl p-4 mt-4 border border-gray-200 relative\">\r\n            <Link href={`/orders/${order.id}`} className=\"block hover:bg-gray-100 transition-colors rounded-xl p-2 -m-2\">\r\n                <div className=\"flex items-center justify-between mb-3\">\r\n                    <h5 className=\"text-base font-semibold text-gray-900\">\r\n                        Заказ #{order.id}\r\n                    </h5>\r\n                    <div className={cn(\r\n                        \"inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium border\",\r\n                        statusConfig.className\r\n                    )}>\r\n                        <StatusIcon className=\"w-3 h-3\" />\r\n                        {statusConfig.label}\r\n                    </div>\r\n                </div>\r\n                <div className=\"space-y-2 text-sm text-gray-600\">\r\n                    <span className=\"text-sm text-gray-500 font-normal\">{order.delivery_type}</span>\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <span>Гость: {order.user.first_name}</span>\r\n                        <span className=\"font-medium text-gray-900\">{order.amount} ₽</span>\r\n                    </div>\r\n                </div>\r\n            </Link>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS,gBAAgB,EAAE,KAAK,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,UAAe,AAAD,EAAE,MAAM,MAAM;IACjD,MAAM,aAAa,aAAa,IAAI;IAEpC,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;YAAE,WAAU;;8BACzC,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;;gCAAwC;gCAC1C,MAAM,EAAE;;;;;;;sCAEpB,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,oFACA,aAAa,SAAS;;8CAEtB,8OAAC;oCAAW,WAAU;;;;;;gCACrB,aAAa,KAAK;;;;;;;;;;;;;8BAG3B,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAK,WAAU;sCAAqC,MAAM,aAAa;;;;;;sCACxE,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;;wCAAK;wCAAQ,MAAM,IAAI,CAAC,UAAU;;;;;;;8CACnC,8OAAC;oCAAK,WAAU;;wCAA6B,MAAM,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlF", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/accordion.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDownIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Accordion({\r\n  ...props\r\n}) {\r\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />;\r\n}\r\n\r\nfunction AccordionItem({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <AccordionPrimitive.Item\r\n      data-slot=\"accordion-item\"\r\n      className={cn(\"border-b last:border-b-0\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction AccordionTrigger({\r\n  className,\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    <AccordionPrimitive.Header className=\"flex\">\r\n      <AccordionPrimitive.Trigger\r\n        data-slot=\"accordion-trigger\"\r\n        className={cn(\r\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:bg-accent/50 focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\r\n          className\r\n        )}\r\n        {...props}>\r\n        {children}\r\n        <ChevronDownIcon\r\n          className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\r\n      </AccordionPrimitive.Trigger>\r\n    </AccordionPrimitive.Header>\r\n  );\r\n}\r\n\r\nfunction AccordionContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}) {\r\n  return (\r\n    <AccordionPrimitive.Content\r\n      data-slot=\"accordion-content\"\r\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\r\n      {...props}>\r\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\r\n    </AccordionPrimitive.Content>\r\n  );\r\n}\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACJ;IACC,qBAAO,8OAAC,qKAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,8OAAC,qKAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,8OAAC,qKAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iTACA;YAED,GAAG,KAAK;;gBACR;8BACD,8OAAC,wNAAA,CAAA,kBAAe;oBACd,WAAU;;;;;;;;;;;;;;;;;AAIpB;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,qBACE,8OAAC,qKAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBACT,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/badge.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OACJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAEf", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/page.js"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { Section } from \"@/components/layout\";\r\nimport OrderReviewCard from \"@/components/shared/OrderReviewCard\";\r\nimport { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from \"@/components/ui/accordion\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Clock, CheckCircle, Truck, Package } from \"lucide-react\";\r\nimport { useOrders } from \"@/contexts/OrdersContext\";\r\nimport { HIDDEN_STATUSES } from \"@/constants/orderStatus\";\r\n\r\nconst statusSections = [\r\n  {\r\n    id: \"item-1\",\r\n    title: \"Новые заказы\",\r\n    status: \"new\",\r\n    count: 3,\r\n    icon: Clock,\r\n    color: \"text-orange-600\",\r\n    bgColor: \"bg-orange-50\",\r\n    borderColor: \"border-orange-200\"\r\n  },\r\n  {\r\n    id: \"item-2\",\r\n    title: \"Подтверждены\",\r\n    status: \"confirmed\",\r\n    count: 10,\r\n    icon: CheckCircle,\r\n    color: \"text-blue-600\",\r\n    bgColor: \"bg-blue-50\",\r\n    borderColor: \"border-blue-200\"\r\n  },\r\n  {\r\n    id: \"item-3\",\r\n    title: \"Готовы\",\r\n    status: \"completed\",\r\n    count: 2,\r\n    icon: Package,\r\n    color: \"text-green-600\",\r\n    bgColor: \"bg-green-50\",\r\n    borderColor: \"border-green-200\"\r\n  },\r\n  {\r\n    id: \"item-4\",\r\n    title: \"В пути\",\r\n    status: \"delivery\",\r\n    count: 80,\r\n    icon: Truck,\r\n    color: \"text-purple-600\",\r\n    bgColor: \"bg-purple-50\",\r\n    borderColor: \"border-purple-200\"\r\n  }\r\n];\r\n\r\nexport default function Home() {\r\n  const { orders, loading, error, getOrdersByStatusSync } = useOrders()\r\n  const [sectionOrders, setSectionOrders] = useState({})\r\n\r\n  // Обновляем заказы для каждой секции при изменении общего списка заказов\r\n  useEffect(() => {\r\n    if (orders.length > 0) {\r\n      const newSectionOrders = {}\r\n      statusSections.forEach(section => {\r\n        // Фильтруем заказы по статусу и исключаем скрытые статусы\r\n        const filteredOrders = getOrdersByStatusSync(section.status)\r\n          .filter(order => !HIDDEN_STATUSES.includes(order.status))\r\n        newSectionOrders[section.status] = filteredOrders\r\n      })\r\n      setSectionOrders(newSectionOrders)\r\n    }\r\n  }, [orders, getOrdersByStatusSync])\r\n\r\n  // Обновляем счетчики в секциях на основе реальных данных\r\n  const updatedStatusSections = statusSections.map(section => ({\r\n    ...section,\r\n    count: sectionOrders[section.status]?.length || 0\r\n  }))\r\n\r\n  if (loading) {\r\n    return (\r\n      <Section spacing=\"sm\">\r\n        <div className=\"flex justify-center items-center h-64\">\r\n          <div className=\"text-lg text-gray-600\">Загрузка заказов...</div>\r\n        </div>\r\n      </Section>\r\n    )\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Section spacing=\"sm\">\r\n        <div className=\"flex justify-center items-center h-64\">\r\n          <div className=\"text-lg text-red-600\">Ошибка загрузки заказов: {error}</div>\r\n        </div>\r\n      </Section>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <Section spacing=\"sm\">\r\n      <Accordion\r\n        type=\"multiple\"\r\n        className=\"space-y-3 bg-gray-50 rounded-xl p-4\"\r\n      >\r\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Заказы</h1>\r\n        {updatedStatusSections.map((section) => {\r\n          const IconComponent = section.icon;\r\n          const sectionOrdersList = sectionOrders[section.status] || []\r\n\r\n          return (\r\n            <AccordionItem\r\n              key={section.id}\r\n              value={section.id}\r\n              className=\"bg-white rounded-xl border border-gray-100 overflow-hidden\"\r\n            >\r\n              <AccordionTrigger className=\"text-lg font-semibold text-gray-900 hover:no-underline px-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <IconComponent className={`w-5 h-5 ${section.color}`} />\r\n                    <span>{section.title}</span>\r\n                  </div>\r\n                  <Badge\r\n                    variant=\"secondary\"\r\n                    className={`font-mono ${section.bgColor} ${section.color} border-0 ml-2`}\r\n                  >\r\n                    {section.count}\r\n                  </Badge>\r\n                </div>\r\n              </AccordionTrigger>\r\n              <AccordionContent className=\"px-4\">\r\n                {sectionOrdersList.length > 0 ? (\r\n                  sectionOrdersList.map(order => (\r\n                    <OrderReviewCard key={order.id} order={order} />\r\n                  ))\r\n                ) : (\r\n                  <div className=\"text-gray-500 text-center py-4\">\r\n                    Нет заказов с статусом \"{section.title}\"\r\n                  </div>\r\n                )}\r\n              </AccordionContent>\r\n            </AccordionItem>\r\n          );\r\n        })}\r\n      </Accordion>\r\n    </Section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,2NAAA,CAAA,cAAW;QACjB,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,SAAS;QACT,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;QACT,aAAa;IACf;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,qBAAqB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAEpD,yEAAyE;IACzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,MAAM,mBAAmB,CAAC;YAC1B,eAAe,OAAO,CAAC,CAAA;gBACrB,0DAA0D;gBAC1D,MAAM,iBAAiB,sBAAsB,QAAQ,MAAM,EACxD,MAAM,CAAC,CAAA,QAAS,CAAC,+HAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC,MAAM,MAAM;gBACzD,gBAAgB,CAAC,QAAQ,MAAM,CAAC,GAAG;YACrC;YACA,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAQ;KAAsB;IAElC,yDAAyD;IACzD,MAAM,wBAAwB,eAAe,GAAG,CAAC,CAAA,UAAW,CAAC;YAC3D,GAAG,OAAO;YACV,OAAO,aAAa,CAAC,QAAQ,MAAM,CAAC,EAAE,UAAU;QAClD,CAAC;IAED,IAAI,SAAS;QACX,qBACE,8OAAC,6KAAA,CAAA,UAAO;YAAC,SAAQ;sBACf,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAwB;;;;;;;;;;;;;;;;IAI/C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,6KAAA,CAAA,UAAO;YAAC,SAAQ;sBACf,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAAuB;wBAA0B;;;;;;;;;;;;;;;;;IAIxE;IAEA,qBACE,8OAAC,6KAAA,CAAA,UAAO;QAAC,SAAQ;kBACf,cAAA,8OAAC,qIAAA,CAAA,YAAS;YACR,MAAK;YACL,WAAU;;8BAEV,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;gBACrD,sBAAsB,GAAG,CAAC,CAAC;oBAC1B,MAAM,gBAAgB,QAAQ,IAAI;oBAClC,MAAM,oBAAoB,aAAa,CAAC,QAAQ,MAAM,CAAC,IAAI,EAAE;oBAE7D,qBACE,8OAAC,qIAAA,CAAA,gBAAa;wBAEZ,OAAO,QAAQ,EAAE;wBACjB,WAAU;;0CAEV,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CAC1B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAc,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;8DACpD,8OAAC;8DAAM,QAAQ,KAAK;;;;;;;;;;;;sDAEtB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAW,CAAC,UAAU,EAAE,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,cAAc,CAAC;sDAEvE,QAAQ,KAAK;;;;;;;;;;;;;;;;;0CAIpB,8OAAC,qIAAA,CAAA,mBAAgB;gCAAC,WAAU;0CACzB,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAA,sBACpB,8OAAC,+IAAA,CAAA,UAAe;wCAAgB,OAAO;uCAAjB,MAAM,EAAE;;;;8DAGhC,8OAAC;oCAAI,WAAU;;wCAAiC;wCACrB,QAAQ,KAAK;wCAAC;;;;;;;;;;;;;uBAzBxC,QAAQ,EAAE;;;;;gBA+BrB;;;;;;;;;;;;AAIR", "debugId": null}}]}