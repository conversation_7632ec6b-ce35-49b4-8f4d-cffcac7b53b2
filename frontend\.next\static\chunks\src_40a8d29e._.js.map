{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAS;;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Container.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\n\r\nexport default function Container({\r\n  children,\r\n  className,\r\n  size = \"default\",\r\n  as: Component = \"div\",\r\n  ...props\r\n}) {\r\n  const sizeVariants = {\r\n    sm: \"max-w-4xl\",\r\n    default: \"max-w-6xl\",\r\n    lg: \"max-w-7xl\",\r\n    full: \"max-w-none\"\r\n  };\r\n\r\n  return (\r\n    <Component\r\n      className={cn(\r\n        \"w-full mx-auto transition-all duration-300 ease-in-out\",\r\n\r\n        sizeVariants[size],\r\n\r\n        \"px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16\",\r\n\r\n        \"relative\",\r\n\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </Component>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,UAAU,KAMjC;QANiC,EAChC,QAAQ,EACR,SAAS,EACT,OAAO,SAAS,EAChB,IAAI,YAAY,KAAK,EACrB,GAAG,OACJ,GANiC;IAOhC,MAAM,eAAe;QACnB,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DAEA,YAAY,CAAC,KAAK,EAElB,0CAEA,YAEA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KAhCwB", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Section.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport Container from \"./Container\";\n\nexport default function Section({ \n  children, \n  className,\n  containerClassName,\n  containerSize = \"default\",\n  spacing = \"default\",\n  background = \"transparent\",\n  as: Component = \"section\",\n  ...props \n}) {\n  const spacingVariants = {\n    none: \"\",\n    sm: \"py-8 md:py-12\",\n    default: \"py-12 md:py-16 lg:py-20\",\n    lg: \"py-16 md:py-20 lg:py-24\",\n    xl: \"py-20 md:py-24 lg:py-32\"\n  };\n\n  const backgroundVariants = {\n    transparent: \"\",\n    white: \"bg-white\",\n    gray: \"bg-gray-50\",\n    primary: \"bg-primary/5\",\n    muted: \"bg-muted\"\n  };\n\n  return (\n    <Component\n      className={cn(\n        // Базовые стили секции\n        \"relative w-full\",\n        \n        // Отступы\n        spacingVariants[spacing],\n        \n        // Фон\n        backgroundVariants[background],\n        \n        className\n      )}\n      {...props}\n    >\n      <Container \n        size={containerSize}\n        className={containerClassName}\n      >\n        {children}\n      </Container>\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,KAS/B;QAT+B,EAC9B,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,SAAS,EACzB,UAAU,SAAS,EACnB,aAAa,aAAa,EAC1B,IAAI,YAAY,SAAS,EACzB,GAAG,OACJ,GAT+B;IAU9B,MAAM,kBAAkB;QACtB,MAAM;QACN,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uBAAuB;QACvB,mBAEA,UAAU;QACV,eAAe,CAAC,QAAQ,EAExB,MAAM;QACN,kBAAkB,CAAC,WAAW,EAE9B;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,4IAAA,CAAA,UAAS;YACR,MAAM;YACN,WAAW;sBAEV;;;;;;;;;;;AAIT;KAlDwB", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Header.jsx"], "sourcesContent": ["import Link from 'next/link';\r\nimport Container from './Container';\r\n\r\nexport default function Header() {\r\n    return (\r\n        <header className=\"sticky top-0 z-50 bg-white/10 backdrop-blur-md\">\r\n            <Container className=\"py-4 relative z-10\">\r\n              <Link href=\"/\">\r\n                <h1 className=\"text-2xl font-bagel-fat-one text-gray-900\">\r\n                  VKUS\r\n                </h1>\r\n              </Link>\r\n            </Container>\r\n        </header>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACpB,qBACI,6LAAC;QAAO,WAAU;kBACd,cAAA,6LAAC,4IAAA,CAAA,UAAS;YAAC,WAAU;sBACnB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,6LAAC;oBAAG,WAAU;8BAA4C;;;;;;;;;;;;;;;;;;;;;AAO1E;KAZwB", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/index.js"], "sourcesContent": ["export { default as Container } from './Container';\nexport { default as Section } from './Section';\nexport { default as Header } from './Header';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/constants/orderStatus.js"], "sourcesContent": ["/**\n * Enum для статусов заказов\n * Содержит все возможные статусы заказов и их строковые значения\n */\nexport const ORDER_STATUS = {\n  NEW: 'new',\n  CONFIRMED: 'confirmed',\n  COMPLETED: 'completed',\n  GIVEN: 'given',        // Отдан (только для самовывоза)\n  DELIVERY: 'delivery',   // В пути (только для доставки)\n  DELIVERED: 'delivered', // Доставлен (только для доставки)\n  CANCELLED: 'cancelled'\n};\n\n/**\n * Массив статусов в порядке их следования в жизненном цикле заказа\n * Базовая цепочка для всех заказов\n */\nexport const STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.DELIVERY,\n  ORDER_STATUS.CANCELLED\n];\n\n/**\n * Цепочка статусов для самовывоза\n */\nexport const PICKUP_STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.GIVEN\n];\n\n/**\n * Цепочка статусов для доставки\n */\nexport const DELIVERY_STATUS_ORDER = [\n  ORDER_STATUS.NEW,\n  ORDER_STATUS.CONFIRMED,\n  ORDER_STATUS.COMPLETED,\n  ORDER_STATUS.DELIVERY,\n  ORDER_STATUS.DELIVERED\n];\n\n/**\n * Статусы, которые не отображаются в интерфейсе (финальные)\n */\nexport const HIDDEN_STATUSES = [\n  ORDER_STATUS.GIVEN,\n  ORDER_STATUS.DELIVERED\n];\n\n/**\n * Проверяет, является ли переданное значение валидным статусом заказа\n * @param {string} status - Статус для проверки\n * @returns {boolean} - true если статус валидный\n */\nexport const isValidStatus = (status) => {\n  return Object.values(ORDER_STATUS).includes(status);\n};\n\n/**\n * Получает все возможные статусы заказов\n * @returns {string[]} - Массив всех статусов\n */\nexport const getAllStatuses = () => {\n  return Object.values(ORDER_STATUS);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AACM,MAAM,eAAe;IAC1B,KAAK;IACL,WAAW;IACX,WAAW;IACX,OAAO;IACP,UAAU;IACV,WAAW;IACX,WAAW;AACb;AAMO,MAAM,eAAe;IAC1B,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,QAAQ;IACrB,aAAa,SAAS;CACvB;AAKM,MAAM,sBAAsB;IACjC,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,KAAK;CACnB;AAKM,MAAM,wBAAwB;IACnC,aAAa,GAAG;IAChB,aAAa,SAAS;IACtB,aAAa,SAAS;IACtB,aAAa,QAAQ;IACrB,aAAa,SAAS;CACvB;AAKM,MAAM,kBAAkB;IAC7B,aAAa,KAAK;IAClB,aAAa,SAAS;CACvB;AAOM,MAAM,gBAAgB,CAAC;IAC5B,OAAO,OAAO,MAAM,CAAC,cAAc,QAAQ,CAAC;AAC9C;AAMO,MAAM,iBAAiB;IAC5B,OAAO,OAAO,MAAM,CAAC;AACvB", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/statusConfig.js"], "sourcesContent": ["import { <PERSON><PERSON><PERSON>t, CheckCircle, Truck, Package, Clock, HandHeart, CheckChe<PERSON> } from \"lucide-react\";\r\nimport { ORDER_STATUS } from '@/constants/orderStatus';\r\n\r\nexport default function getStatusConfig(status) {\r\n    const configs = {\r\n        [ORDER_STATUS.NEW]: {\r\n            icon: Clock,\r\n            label: \"Новый\",\r\n            buttonLabel: \"Подтвердить\",\r\n            className: \"bg-orange-50 text-orange-600 border-orange-200\"\r\n        },\r\n        [ORDER_STATUS.CONFIRMED]: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвержден\",\r\n            buttonLabel: \"Готов\",\r\n            className: \"bg-blue-50 text-blue-600 border-blue-200\"\r\n        },\r\n        [ORDER_STATUS.COMPLETED]: {\r\n            icon: Package,\r\n            label: \"Готов\",\r\n            buttonLabel: \"Отдать\",\r\n            className: \"bg-green-50 text-green-600 border-green-200\"\r\n        },\r\n        [ORDER_STATUS.GIVEN]: {\r\n            icon: HandHeart,\r\n            label: \"Отдан\",\r\n            buttonLabel: \"Отдан\",\r\n            className: \"bg-emerald-50 text-emerald-600 border-emerald-200\"\r\n        },\r\n        [ORDER_STATUS.DELIVERY]: {\r\n            icon: Truck,\r\n            label: \"В пути\",\r\n            buttonLabel: \"Доставлен\",\r\n            className: \"bg-purple-50 text-purple-600 border-purple-200\"\r\n        },\r\n        [ORDER_STATUS.DELIVERED]: {\r\n            icon: CheckCheck,\r\n            label: \"Доставлен\",\r\n            buttonLabel: \"Доставлен\",\r\n            className: \"bg-indigo-50 text-indigo-600 border-indigo-200\"\r\n        },\r\n        [ORDER_STATUS.CANCELLED]: {\r\n            icon: TriangleAlert,\r\n            label: \"Отменен\",\r\n            buttonLabel: \"Отменен\",\r\n            className: \"bg-red-50 text-red-600 border-red-200\"\r\n        }\r\n    };\r\n    return configs[status] || configs[ORDER_STATUS.NEW];\r\n}\r\n\r\n/**\r\n * Получает конфигурацию для кнопки перехода к следующему статусу\r\n * @param {string} nextStatus - Следующий статус\r\n * @returns {object} - Конфигурация кнопки\r\n */\r\nexport function getNextStatusButtonConfig(nextStatus) {\r\n    const buttonConfigs = {\r\n        [ORDER_STATUS.CONFIRMED]: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвердить\",\r\n            className: \"bg-blue-500 text-white hover:bg-blue-600\"\r\n        },\r\n        [ORDER_STATUS.COMPLETED]: {\r\n            icon: Package,\r\n            label: \"Готов\",\r\n            className: \"bg-green-500 text-white hover:bg-green-600\"\r\n        },\r\n        [ORDER_STATUS.GIVEN]: {\r\n            icon: HandHeart,\r\n            label: \"Отдать\",\r\n            className: \"bg-emerald-500 text-white hover:bg-emerald-600\"\r\n        },\r\n        [ORDER_STATUS.DELIVERY]: {\r\n            icon: Truck,\r\n            label: \"В доставку\",\r\n            className: \"bg-purple-500 text-white hover:bg-purple-600\"\r\n        },\r\n        [ORDER_STATUS.DELIVERED]: {\r\n            icon: CheckCheck,\r\n            label: \"Доставлен\",\r\n            className: \"bg-indigo-500 text-white hover:bg-indigo-600\"\r\n        }\r\n    };\r\n\r\n    return buttonConfigs[nextStatus] || {\r\n        icon: CheckCircle,\r\n        label: \"Далее\",\r\n        className: \"bg-gray-500 text-white hover:bg-gray-600\"\r\n    };\r\n}"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAEe,SAAS,gBAAgB,MAAM;IAC1C,MAAM,UAAU;QACZ,CAAC,kIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,EAAE;YAChB,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,8NAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE;YAClB,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,EAAE;YACrB,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;YACb,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;YACb,WAAW;QACf;IACJ;IACA,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,kIAAA,CAAA,eAAY,CAAC,GAAG,CAAC;AACvD;AAOO,SAAS,0BAA0B,UAAU;IAChD,MAAM,gBAAgB;QAClB,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,8NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE;YAClB,MAAM,mNAAA,CAAA,YAAS;YACf,OAAO;YACP,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,EAAE;YACrB,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,WAAW;QACf;QACA,CAAC,kIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,EAAE;YACtB,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,WAAW;QACf;IACJ;IAEA,OAAO,aAAa,CAAC,WAAW,IAAI;QAChC,MAAM,8NAAA,CAAA,cAAW;QACjB,OAAO;QACP,WAAW;IACf;AACJ", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/shared/OrderCard.jsx"], "sourcesContent": ["import {\r\n  User,\r\n  Phone,\r\n  Truck,\r\n  MessageSquare,\r\n  CreditCard,\r\n  MapPin,\r\n  Package\r\n} from \"lucide-react\";\r\nimport getStatusConfig from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function OrderCard({ order }) {\r\n  const statusConfig = getStatusConfig(order.status);\r\n  const StatusIcon = statusConfig.icon;\r\n\r\n  return (\r\n    <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300 mb-8\">\r\n      {/* Заголовок с номером заказа и статусом */}\r\n      <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900\">\r\n            Заказ #{order.id}\r\n          </h3>\r\n          <div className={cn(\r\n            \"inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border\",\r\n            statusConfig.className\r\n          )}>\r\n            <StatusIcon className=\"w-4 h-4\" />\r\n            {statusConfig.label}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-6 space-y-6\">\r\n        {/* Информация о клиенте */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <User className=\"w-5 h-5 text-gray-600\" />\r\n            Информация о клиенте\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4 space-y-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <User className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900 font-medium\">{order.user.first_name}</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-3\">\r\n              <Phone className=\"w-4 h-4 text-gray-500\" />\r\n              <a\r\n                href={`tel:${order.user.phone}`}\r\n                className=\"text-blue-600 hover:text-blue-800 transition-colors\"\r\n              >\r\n                {order.user.phone}\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Детали доставки */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <Truck className=\"w-5 h-5 text-gray-600\" />\r\n            Доставка\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4 space-y-3\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <MapPin className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900\">{order.delivery_type}</span>\r\n            </div>\r\n            {order.address && (\r\n              <div className=\"flex items-start gap-3\">\r\n                <MapPin className=\"w-4 h-4 text-gray-500 mt-0.5\" />\r\n                <span className=\"text-gray-700\">{order.address}</span>\r\n              </div>\r\n            )}\r\n            {order.comment && (\r\n              <div className=\"flex items-start gap-3\">\r\n                <MessageSquare className=\"w-4 h-4 text-gray-500 mt-0.5\" />\r\n                <span className=\"text-gray-700 italic\">\"{order.comment}\"</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Способ оплаты */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <CreditCard className=\"w-5 h-5 text-gray-600\" />\r\n            Способ оплаты\r\n          </h4>\r\n          <div className=\"bg-gray-50 rounded-xl p-4\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <CreditCard className=\"w-4 h-4 text-gray-500\" />\r\n              <span className=\"text-gray-900\">{order.paymentsystem}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Список блюд */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-base font-semibold text-gray-900 flex items-center gap-2\">\r\n            <Package className=\"w-5 h-5 text-gray-600\" />\r\n            Состав заказа\r\n          </h4>\r\n          <div className=\"space-y-3\">\r\n            {order.meals.map((meal) => (\r\n              <div\r\n                key={meal.id}\r\n                className=\"bg-gray-50 rounded-lg p-3 flex items-center justify-between hover:bg-gray-100 transition-colors\"\r\n              >\r\n                <div className=\"flex-1\">\r\n                  <h5 className=\"font-medium text-gray-900 mb-2\">{meal.name}</h5>\r\n                  <div className=\"flex items-center gap-3 text-sm\">\r\n                    <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\">\r\n                      {Math.floor(meal.quantity / parseInt(meal.portion))} шт\r\n                    </span>\r\n                    <span className=\"text-gray-600\">\r\n                      по {meal.portion} {meal.unit}\r\n                    </span>\r\n                    <span className=\"text-gray-500\">\r\n                      • {meal.quantity} {meal.unit}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-right ml-4\">\r\n                  <div className=\"text-lg font-semibold text-gray-900\">\r\n                    {meal.amount} ₽\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Итоговая сумма */}\r\n        <div className=\"border-t border-gray-200 pt-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-lg font-semibold text-gray-900\">Итого к оплате:</span>\r\n            <span className=\"text-2xl font-bold text-gray-900\">{order.amount} ₽</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;;;AAEe,SAAS,UAAU,KAAS;QAAT,EAAE,KAAK,EAAE,GAAT;IAChC,MAAM,eAAe,CAAA,GAAA,+HAAA,CAAA,UAAe,AAAD,EAAE,MAAM,MAAM;IACjD,MAAM,aAAa,aAAa,IAAI;IAEpC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCAAsC;gCAC1C,MAAM,EAAE;;;;;;;sCAElB,6LAAC;4BAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,sFACA,aAAa,SAAS;;8CAEtB,6LAAC;oCAAW,WAAU;;;;;;gCACrB,aAAa,KAAK;;;;;;;;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAA6B,MAAM,IAAI,CAAC,UAAU;;;;;;;;;;;;kDAEpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDACC,MAAM,AAAC,OAAuB,OAAjB,MAAM,IAAI,CAAC,KAAK;gDAC7B,WAAU;0DAET,MAAM,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAiB,MAAM,aAAa;;;;;;;;;;;;oCAErD,MAAM,OAAO,kBACZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAiB,MAAM,OAAO;;;;;;;;;;;;oCAGjD,MAAM,OAAO,kBACZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAK,WAAU;;oDAAuB;oDAAE,MAAM,OAAO;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAGlD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAiB,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;kCAM1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;0CAG/C,6LAAC;gCAAI,WAAU;0CACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAkC,KAAK,IAAI;;;;;;kEACzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEACb,KAAK,KAAK,CAAC,KAAK,QAAQ,GAAG,SAAS,KAAK,OAAO;oEAAG;;;;;;;0EAEtD,6LAAC;gEAAK,WAAU;;oEAAgB;oEAC1B,KAAK,OAAO;oEAAC;oEAAE,KAAK,IAAI;;;;;;;0EAE9B,6LAAC;gEAAK,WAAU;;oEAAgB;oEAC3B,KAAK,QAAQ;oEAAC;oEAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;0DAIlC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,KAAK,MAAM;wDAAC;;;;;;;;;;;;;uCAnBZ,KAAK,EAAE;;;;;;;;;;;;;;;;kCA4BpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAsC;;;;;;8CACtD,6LAAC;oCAAK,WAAU;;wCAAoC,MAAM,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7E;KApIwB", "debugId": null}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/getNextStatus.js"], "sourcesContent": ["import { PICKUP_STATUS_ORDER, DELIVERY_STATUS_ORDER } from '@/constants/orderStatus';\r\n\r\n/**\r\n * Получает следующий статус в зависимости от типа доставки\r\n * @param {string} currentStatus - Текущий статус заказа\r\n * @param {string} deliveryType - Тип доставки (\"Самовывоз\" или \"Доставка\")\r\n * @returns {string|null} - Следующий статус или null, если это финальный статус\r\n */\r\nexport default function getNextStatus(currentStatus, deliveryType) {\r\n    const isPickup = deliveryType === \"Самовывоз\";\r\n    const statusOrder = isPickup ? PICKUP_STATUS_ORDER : DELIVERY_STATUS_ORDER;\r\n\r\n    const currentIndex = statusOrder.indexOf(currentStatus);\r\n\r\n    // Если статус не найден или это последний статус в цепочке\r\n    if (currentIndex === -1 || currentIndex >= statusOrder.length - 1) {\r\n        return null;\r\n    }\r\n\r\n    return statusOrder[currentIndex + 1];\r\n}"], "names": [], "mappings": ";;;AAAA;;AAQe,SAAS,cAAc,aAAa,EAAE,YAAY;IAC7D,MAAM,WAAW,iBAAiB;IAClC,MAAM,cAAc,WAAW,kIAAA,CAAA,sBAAmB,GAAG,kIAAA,CAAA,wBAAqB;IAE1E,MAAM,eAAe,YAAY,OAAO,CAAC;IAEzC,2DAA2D;IAC3D,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,YAAY,MAAM,GAAG,GAAG;QAC/D,OAAO;IACX;IAEA,OAAO,WAAW,CAAC,eAAe,EAAE;AACxC", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/NextStatusButton.jsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport getNextStatus from \"@/utils/getNextStatus\";\r\nimport { getNextStatusButtonConfig } from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useOrders } from \"@/contexts/OrdersContext\";\r\nimport { ChevronRight } from \"lucide-react\";\r\n\r\nexport default function NextStatusButton({ currentStatus, deliveryType, orderId, onStatusUpdate }) {\r\n    const { updateOrderStatus } = useOrders()\r\n    const [nextStatus, setNextStatus] = useState(null)\r\n    const [isUpdating, setIsUpdating] = useState(false)\r\n    const [error, setError] = useState(null)\r\n\r\n    // Состояния для свайп-жеста\r\n    const [swipeProgress, setSwipeProgress] = useState(0)\r\n    const [isSwipeActive, setIsSwipeActive] = useState(false)\r\n    const [isSwipeCompleted, setIsSwipeCompleted] = useState(false)\r\n    const buttonRef = useRef(null)\r\n    const startXRef = useRef(0)\r\n    const currentXRef = useRef(0)\r\n\r\n    useEffect(() => {\r\n        setNextStatus(getNextStatus(currentStatus, deliveryType))\r\n    }, [currentStatus, deliveryType])\r\n\r\n    // Автоматически скрываем ошибку через 5 секунд\r\n    useEffect(() => {\r\n        if (error) {\r\n            const timer = setTimeout(() => {\r\n                setError(null)\r\n            }, 5000)\r\n            return () => clearTimeout(timer)\r\n        }\r\n    }, [error])\r\n\r\n    // Обработчики свайп-жестов\r\n    const handleSwipeStart = (clientX) => {\r\n        if (isUpdating) return\r\n\r\n        setIsSwipeActive(true)\r\n        setIsSwipeCompleted(false)\r\n        setSwipeProgress(0)\r\n        startXRef.current = clientX\r\n        currentXRef.current = clientX\r\n    }\r\n\r\n    const handleSwipeMove = (clientX) => {\r\n        if (!isSwipeActive || isUpdating) return\r\n\r\n        currentXRef.current = clientX\r\n        const deltaX = clientX - startXRef.current\r\n        const buttonWidth = buttonRef.current?.offsetWidth || 0\r\n\r\n        // Более чувствительный расчет прогресса\r\n        const progress = Math.max(0, Math.min(1, deltaX / (buttonWidth * 0.6))) // 60% для активации\r\n\r\n        setSwipeProgress(progress)\r\n\r\n        if (progress >= 1 && !isSwipeCompleted) {\r\n            setIsSwipeCompleted(true)\r\n\r\n            // Тактильная обратная связь на мобильных устройствах\r\n            if (navigator.vibrate) {\r\n                navigator.vibrate(50)\r\n            }\r\n\r\n            // Добавляем небольшую задержку для лучшего UX\r\n            setTimeout(() => {\r\n                handleSwipeComplete()\r\n            }, 150)\r\n        }\r\n    }\r\n\r\n    const handleSwipeEnd = () => {\r\n        if (!isSwipeCompleted) {\r\n            // Плавная анимация возврата в исходное положение\r\n            const resetProgress = () => {\r\n                setSwipeProgress(prev => {\r\n                    const newProgress = prev * 0.8\r\n                    if (newProgress > 0.01) {\r\n                        requestAnimationFrame(resetProgress)\r\n                        return newProgress\r\n                    }\r\n                    return 0\r\n                })\r\n            }\r\n            resetProgress()\r\n        }\r\n        setIsSwipeActive(false)\r\n    }\r\n\r\n    // Обработчики для мыши\r\n    const handleMouseDown = (e) => {\r\n        e.preventDefault()\r\n        e.stopPropagation()\r\n        handleSwipeStart(e.clientX)\r\n\r\n        const handleMouseMove = (e) => {\r\n            e.preventDefault()\r\n            handleSwipeMove(e.clientX)\r\n        }\r\n        const handleMouseUp = (e) => {\r\n            e.preventDefault()\r\n            handleSwipeEnd()\r\n            document.removeEventListener('mousemove', handleMouseMove)\r\n            document.removeEventListener('mouseup', handleMouseUp)\r\n        }\r\n\r\n        document.addEventListener('mousemove', handleMouseMove, { passive: false })\r\n        document.addEventListener('mouseup', handleMouseUp, { passive: false })\r\n    }\r\n\r\n    // Обработчики для touch\r\n    const handleTouchStart = (e) => {\r\n        e.stopPropagation()\r\n        handleSwipeStart(e.touches[0].clientX)\r\n    }\r\n\r\n    const handleTouchMove = (e) => {\r\n        e.preventDefault()\r\n        e.stopPropagation()\r\n        handleSwipeMove(e.touches[0].clientX)\r\n    }\r\n\r\n    const handleTouchEnd = (e) => {\r\n        e.stopPropagation()\r\n        handleSwipeEnd()\r\n    }\r\n\r\n    const handleSwipeComplete = async () => {\r\n        if (!nextStatus || isUpdating) return\r\n\r\n        // Выполняем действие обновления статуса\r\n        await handleStatusUpdate()\r\n\r\n        // Сбрасываем состояние свайпа\r\n        setTimeout(() => {\r\n            setSwipeProgress(0)\r\n            setIsSwipeCompleted(false)\r\n        }, 300)\r\n    }\r\n\r\n    const buttonConfig = nextStatus ? getNextStatusButtonConfig(nextStatus) : null\r\n    const StatusIcon = buttonConfig?.icon\r\n\r\n    // 🎨 Функция для определения цветовой схемы на основе статуса\r\n    const getStatusColorScheme = () => {\r\n        if (!buttonConfig) return \"bg-gradient-to-r from-gray-500 to-gray-600 text-white\"\r\n\r\n        // Определяем цвет на основе следующего статуса\r\n        if (nextStatus?.includes('confirmed')) {\r\n            return \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\"\r\n        } else if (nextStatus?.includes('completed')) {\r\n            return \"bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700\"\r\n        } else if (nextStatus?.includes('given')) {\r\n            return \"bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700\"\r\n        } else if (nextStatus?.includes('delivery')) {\r\n            return \"bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:from-purple-600 hover:to-purple-700\"\r\n        } else if (nextStatus?.includes('delivered')) {\r\n            return \"bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:from-indigo-600 hover:to-indigo-700\"\r\n        } else {\r\n            return \"bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700\"\r\n        }\r\n    }\r\n\r\n    const handleStatusUpdate = async () => {\r\n        if (!nextStatus || isUpdating) return\r\n\r\n        setIsUpdating(true)\r\n        setError(null)\r\n\r\n        try {\r\n            const success = await updateOrderStatus(orderId, nextStatus)\r\n            if (success) {\r\n                // Уведомляем родительский компонент об успешном обновлении\r\n                if (onStatusUpdate) {\r\n                    onStatusUpdate(nextStatus)\r\n                }\r\n            } else {\r\n                setError('Не удалось обновить статус заказа')\r\n            }\r\n        } catch (error) {\r\n            console.error('Ошибка при обновлении статуса:', error)\r\n            setError(error.message || 'Произошла ошибка при обновлении статуса')\r\n        } finally {\r\n            setIsUpdating(false)\r\n        }\r\n    }\r\n\r\n    if (!nextStatus || !buttonConfig) {\r\n        return null\r\n    }\r\n\r\n    return (\r\n        <div className=\"sticky bottom-0 w-full bg-white/80 backdrop-blur-md border-t border-gray-200/50\">\r\n            {error && (\r\n                <div className=\"mx-4 mt-4 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-lg shadow-sm\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                        <div className=\"w-5 h-5 bg-red-500 rounded-full flex items-center justify-center\">\r\n                            <span className=\"text-white text-xs font-bold\">!</span>\r\n                        </div>\r\n                        <span className=\"text-red-700 font-medium\">{error}</span>\r\n                    </div>\r\n                </div>\r\n            )}\r\n\r\n            <div className=\"p-4\">\r\n                <div className=\"relative overflow-hidden rounded-xl shadow-lg\">\r\n                    <button\r\n                        ref={buttonRef}\r\n                        disabled={isUpdating}\r\n                        className={cn(\r\n                            \"relative w-full h-20 font-semibold px-6 py-4 transition-all duration-500 ease-out\",\r\n                            \"select-none touch-none rounded-xl border-0 outline-none focus:outline-none\",\r\n                            \"backdrop-blur-sm\",\r\n                            isUpdating ? \"opacity-60 cursor-not-allowed\" : \"cursor-grab active:cursor-grabbing\",\r\n                            isSwipeActive ? \"scale-[1.01] shadow-2xl\" : \"shadow-lg hover:shadow-xl\",\r\n                            isSwipeCompleted ? \"scale-[1.02] shadow-2xl\" : \"\",\r\n                            getStatusColorScheme()\r\n                        )}\r\n                        onMouseDown={handleMouseDown}\r\n                        onTouchStart={handleTouchStart}\r\n                        onTouchMove={handleTouchMove}\r\n                        onTouchEnd={handleTouchEnd}\r\n                        style={{\r\n                            WebkitTouchCallout: 'none',\r\n                            WebkitUserSelect: 'none',\r\n                            touchAction: 'none'\r\n                        }}\r\n                    >\r\n                        {/* Стильная фоновая заливка прогресса */}\r\n                        <div\r\n                            className=\"absolute inset-0 rounded-xl transition-all duration-300 ease-out\"\r\n                            style={{\r\n                                background: `linear-gradient(90deg,\r\n                                    rgba(255,255,255,0.25) 0%,\r\n                                    rgba(255,255,255,0.15) 50%,\r\n                                    rgba(255,255,255,0.05) 100%)`,\r\n                                width: `${swipeProgress * 100}%`,\r\n                                opacity: isSwipeActive ? 1 : 0,\r\n                                boxShadow: isSwipeActive ? 'inset 0 0 20px rgba(255,255,255,0.2)' : 'none'\r\n                            }}\r\n                        />\r\n\r\n                        {/* Анимированный индикатор прогресса */}\r\n                        {isSwipeActive && (\r\n                            <div\r\n                                className=\"absolute top-0 h-full transition-all duration-200 ease-out\"\r\n                                style={{\r\n                                    right: `${(1 - swipeProgress) * 100}%`,\r\n                                    width: '4px',\r\n                                    background: 'linear-gradient(180deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%)',\r\n                                    borderRadius: '0 2px 2px 0',\r\n                                    opacity: swipeProgress > 0.1 ? 1 : 0,\r\n                                    boxShadow: '0 0 10px rgba(255,255,255,0.5)'\r\n                                }}\r\n                            />\r\n                        )}\r\n\r\n                        {/* Эффект завершения */}\r\n                        {isSwipeCompleted && (\r\n                            <div className=\"absolute inset-0 rounded-xl bg-white/20 animate-pulse\" />\r\n                        )}\r\n\r\n                        {/* Стильный основной контент */}\r\n                        <div className=\"relative z-10 flex items-center justify-between h-full\">\r\n                            <div className=\"flex items-center gap-4\">\r\n                                {isUpdating ? (\r\n                                    <div className=\"w-6 h-6 border-3 border-white/30 border-t-white rounded-full animate-spin\" />\r\n                                ) : (\r\n                                    <div className=\"relative\">\r\n                                        <StatusIcon className=\"w-6 h-6 drop-shadow-sm\" />\r\n                                        {isSwipeCompleted && (\r\n                                            <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full flex items-center justify-center\">\r\n                                                <div className=\"w-1.5 h-1.5 bg-green-500 rounded-full\"></div>\r\n                                            </div>\r\n                                        )}\r\n                                    </div>\r\n                                )}\r\n                                <div className=\"flex flex-col justify-center\">\r\n                                    <span className=\"text-lg font-light tracking-wide drop-shadow-sm\">\r\n                                        {isUpdating ? \"Обновление...\" :\r\n                                         isSwipeCompleted ? \"Выполнено!\" :\r\n                                         isSwipeActive ? `${Math.round(swipeProgress * 100)}%` :\r\n                                         buttonConfig.label}\r\n                                    </span>\r\n                                    {!isUpdating && !isSwipeCompleted && (\r\n                                        <span className=\"text-sm opacity-90 font-medium tracking-wide\">\r\n                                            {isSwipeActive ? \"Продолжайте...\" : \"Проведите для подтверждения\"}\r\n                                        </span>\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n\r\n                            {/* Улучшенные визуальные подсказки */}\r\n                            <div className=\"flex items-center\">\r\n                                {isSwipeCompleted ? (\r\n                                    <div className=\"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm\">\r\n                                        <div className=\"text-white text-lg font-bold\">✓</div>\r\n                                    </div>\r\n                                ) : !isUpdating && !isSwipeActive ? (\r\n                                    <div className=\"flex items-center gap-1 opacity-70\">\r\n                                        <div className=\"flex items-center animate-pulse\">\r\n                                            <ChevronRight className=\"w-5 h-5 drop-shadow-sm\" />\r\n                                            <ChevronRight className=\"w-5 h-5 -ml-2 drop-shadow-sm\" />\r\n                                            <ChevronRight className=\"w-5 h-5 -ml-2 drop-shadow-sm\" />\r\n                                        </div>\r\n                                    </div>\r\n                                ) : isSwipeActive ? (\r\n                                    <div className=\"flex items-center gap-2\">\r\n                                        <div className=\"text-white text-lg font-bold drop-shadow-sm\">\r\n                                            {swipeProgress >= 0.8 ? \"Отпустите!\" : \"→\"}\r\n                                        </div>\r\n                                        {swipeProgress >= 0.8 && (\r\n                                            <div className=\"w-2 h-2 bg-white rounded-full animate-ping\"></div>\r\n                                        )}\r\n                                    </div>\r\n                                ) : null}\r\n                            </div>\r\n                        </div>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS,iBAAiB,KAAwD;QAAxD,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,EAAE,cAAc,EAAE,GAAxD;;IACrC,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,YAAS,AAAD;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,4BAA4B;IAC5B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,cAAc,CAAA,GAAA,gIAAA,CAAA,UAAa,AAAD,EAAE,eAAe;QAC/C;qCAAG;QAAC;QAAe;KAAa;IAEhC,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACN,IAAI,OAAO;gBACP,MAAM,QAAQ;wDAAW;wBACrB,SAAS;oBACb;uDAAG;gBACH;kDAAO,IAAM,aAAa;;YAC9B;QACJ;qCAAG;QAAC;KAAM;IAEV,2BAA2B;IAC3B,MAAM,mBAAmB,CAAC;QACtB,IAAI,YAAY;QAEhB,iBAAiB;QACjB,oBAAoB;QACpB,iBAAiB;QACjB,UAAU,OAAO,GAAG;QACpB,YAAY,OAAO,GAAG;IAC1B;IAEA,MAAM,kBAAkB,CAAC;YAKD;QAJpB,IAAI,CAAC,iBAAiB,YAAY;QAElC,YAAY,OAAO,GAAG;QACtB,MAAM,SAAS,UAAU,UAAU,OAAO;QAC1C,MAAM,cAAc,EAAA,qBAAA,UAAU,OAAO,cAAjB,yCAAA,mBAAmB,WAAW,KAAI;QAEtD,wCAAwC;QACxC,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC,cAAc,GAAG,IAAI,oBAAoB;;QAE5F,iBAAiB;QAEjB,IAAI,YAAY,KAAK,CAAC,kBAAkB;YACpC,oBAAoB;YAEpB,qDAAqD;YACrD,IAAI,UAAU,OAAO,EAAE;gBACnB,UAAU,OAAO,CAAC;YACtB;YAEA,8CAA8C;YAC9C,WAAW;gBACP;YACJ,GAAG;QACP;IACJ;IAEA,MAAM,iBAAiB;QACnB,IAAI,CAAC,kBAAkB;YACnB,iDAAiD;YACjD,MAAM,gBAAgB;gBAClB,iBAAiB,CAAA;oBACb,MAAM,cAAc,OAAO;oBAC3B,IAAI,cAAc,MAAM;wBACpB,sBAAsB;wBACtB,OAAO;oBACX;oBACA,OAAO;gBACX;YACJ;YACA;QACJ;QACA,iBAAiB;IACrB;IAEA,uBAAuB;IACvB,MAAM,kBAAkB,CAAC;QACrB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,iBAAiB,EAAE,OAAO;QAE1B,MAAM,kBAAkB,CAAC;YACrB,EAAE,cAAc;YAChB,gBAAgB,EAAE,OAAO;QAC7B;QACA,MAAM,gBAAgB,CAAC;YACnB,EAAE,cAAc;YAChB;YACA,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;QAC5C;QAEA,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;YAAE,SAAS;QAAM;QACzE,SAAS,gBAAgB,CAAC,WAAW,eAAe;YAAE,SAAS;QAAM;IACzE;IAEA,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACtB,EAAE,eAAe;QACjB,iBAAiB,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;IACzC;IAEA,MAAM,kBAAkB,CAAC;QACrB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,gBAAgB,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;IACxC;IAEA,MAAM,iBAAiB,CAAC;QACpB,EAAE,eAAe;QACjB;IACJ;IAEA,MAAM,sBAAsB;QACxB,IAAI,CAAC,cAAc,YAAY;QAE/B,wCAAwC;QACxC,MAAM;QAEN,8BAA8B;QAC9B,WAAW;YACP,iBAAiB;YACjB,oBAAoB;QACxB,GAAG;IACP;IAEA,MAAM,eAAe,aAAa,CAAA,GAAA,+HAAA,CAAA,4BAAyB,AAAD,EAAE,cAAc;IAC1E,MAAM,aAAa,yBAAA,mCAAA,aAAc,IAAI;IAErC,8DAA8D;IAC9D,MAAM,uBAAuB;QACzB,IAAI,CAAC,cAAc,OAAO;QAE1B,+CAA+C;QAC/C,IAAI,uBAAA,iCAAA,WAAY,QAAQ,CAAC,cAAc;YACnC,OAAO;QACX,OAAO,IAAI,uBAAA,iCAAA,WAAY,QAAQ,CAAC,cAAc;YAC1C,OAAO;QACX,OAAO,IAAI,uBAAA,iCAAA,WAAY,QAAQ,CAAC,UAAU;YACtC,OAAO;QACX,OAAO,IAAI,uBAAA,iCAAA,WAAY,QAAQ,CAAC,aAAa;YACzC,OAAO;QACX,OAAO,IAAI,uBAAA,iCAAA,WAAY,QAAQ,CAAC,cAAc;YAC1C,OAAO;QACX,OAAO;YACH,OAAO;QACX;IACJ;IAEA,MAAM,qBAAqB;QACvB,IAAI,CAAC,cAAc,YAAY;QAE/B,cAAc;QACd,SAAS;QAET,IAAI;YACA,MAAM,UAAU,MAAM,kBAAkB,SAAS;YACjD,IAAI,SAAS;gBACT,2DAA2D;gBAC3D,IAAI,gBAAgB;oBAChB,eAAe;gBACnB;YACJ,OAAO;gBACH,SAAS;YACb;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SAAS,MAAM,OAAO,IAAI;QAC9B,SAAU;YACN,cAAc;QAClB;IACJ;IAEA,IAAI,CAAC,cAAc,CAAC,cAAc;QAC9B,OAAO;IACX;IAEA,qBACI,6LAAC;QAAI,WAAU;;YACV,uBACG,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;sCAEnD,6LAAC;4BAAK,WAAU;sCAA4B;;;;;;;;;;;;;;;;;0BAKxD,6LAAC;gBAAI,WAAU;0BACX,cAAA,6LAAC;oBAAI,WAAU;8BACX,cAAA,6LAAC;wBACG,KAAK;wBACL,UAAU;wBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,qFACA,8EACA,oBACA,aAAa,kCAAkC,sCAC/C,gBAAgB,4BAA4B,6BAC5C,mBAAmB,4BAA4B,IAC/C;wBAEJ,aAAa;wBACb,cAAc;wBACd,aAAa;wBACb,YAAY;wBACZ,OAAO;4BACH,oBAAoB;4BACpB,kBAAkB;4BAClB,aAAa;wBACjB;;0CAGA,6LAAC;gCACG,WAAU;gCACV,OAAO;oCACH,YAAa;oCAIb,OAAO,AAAC,GAAsB,OAApB,gBAAgB,KAAI;oCAC9B,SAAS,gBAAgB,IAAI;oCAC7B,WAAW,gBAAgB,yCAAyC;gCACxE;;;;;;4BAIH,+BACG,6LAAC;gCACG,WAAU;gCACV,OAAO;oCACH,OAAO,AAAC,GAA4B,OAA1B,CAAC,IAAI,aAAa,IAAI,KAAI;oCACpC,OAAO;oCACP,YAAY;oCACZ,cAAc;oCACd,SAAS,gBAAgB,MAAM,IAAI;oCACnC,WAAW;gCACf;;;;;;4BAKP,kCACG,6LAAC;gCAAI,WAAU;;;;;;0CAInB,6LAAC;gCAAI,WAAU;;kDACX,6LAAC;wCAAI,WAAU;;4CACV,2BACG,6LAAC;gDAAI,WAAU;;;;;qEAEf,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAW,WAAU;;;;;;oDACrB,kCACG,6LAAC;wDAAI,WAAU;kEACX,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;0DAK/B,6LAAC;gDAAI,WAAU;;kEACX,6LAAC;wDAAK,WAAU;kEACX,aAAa,kBACb,mBAAmB,eACnB,gBAAgB,AAAC,GAAkC,OAAhC,KAAK,KAAK,CAAC,gBAAgB,MAAK,OACnD,aAAa,KAAK;;;;;;oDAEtB,CAAC,cAAc,CAAC,kCACb,6LAAC;wDAAK,WAAU;kEACX,gBAAgB,mBAAmB;;;;;;;;;;;;;;;;;;kDAOpD,6LAAC;wCAAI,WAAU;kDACV,iCACG,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAI,WAAU;0DAA+B;;;;;;;;;;mDAElD,CAAC,cAAc,CAAC,8BAChB,6LAAC;4CAAI,WAAU;sDACX,cAAA,6LAAC;gDAAI,WAAU;;kEACX,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;mDAGhC,8BACA,6LAAC;4CAAI,WAAU;;8DACX,6LAAC;oDAAI,WAAU;8DACV,iBAAiB,MAAM,eAAe;;;;;;gDAE1C,iBAAiB,qBACd,6LAAC;oDAAI,WAAU;;;;;;;;;;;mDAGvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC;GA9TwB;;QACU,oIAAA,CAAA,YAAS;;;KADnB", "debugId": null}}, {"offset": {"line": 1394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/orders/%5BorderId%5D/page.js"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { Section } from \"@/components/layout\";\r\nimport OrderCard from \"@/components/shared/OrderCard\";\r\nimport NextStatusButton from \"@/components/ui/NextStatusButton\";\r\nimport { useOrders } from \"@/contexts/OrdersContext\";\r\n\r\nexport default function Order({ params }) {\r\n  const { getOrderById } = useOrders()\r\n  const { orderId } = React.use(params)\r\n\r\n  const [orderData, setOrderData] = useState(null)\r\n\r\n  const handleStatusUpdate = (newStatus) => {\r\n    // Обновляем локальное состояние заказа\r\n    setOrderData(prevOrder => ({\r\n      ...prevOrder,\r\n      status: newStatus\r\n    }))\r\n  }\r\n\r\n  useEffect(() => {\r\n    async function loadOrder(orderId) {\r\n      try {\r\n        const order = await getOrderById(orderId)\r\n        console.log(order);\r\n        \r\n        setOrderData(order)\r\n      } catch (error) {\r\n        console.error('Ошибка при загрузке заказа:', error)\r\n      }\r\n    }\r\n    loadOrder(orderId)\r\n  }, [orderId])\r\n\r\n  if (!orderData) {\r\n    return (\r\n      <Section spacing=\"xs\">\r\n        <div className=\"flex justify-center items-center h-64\">\r\n          <div className=\"text-lg text-gray-600\">Загрузка заказа...</div>\r\n        </div>\r\n      </Section>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <main>\r\n      <Section spacing=\"xs\">\r\n        <OrderCard order={orderData} />\r\n      </Section>\r\n      <NextStatusButton\r\n        currentStatus={orderData.status}\r\n        deliveryType={orderData.delivery_type}\r\n        orderId={orderData.id}\r\n        onStatusUpdate={handleStatusUpdate}\r\n      />\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS,MAAM,KAAU;QAAV,EAAE,MAAM,EAAE,GAAV;;IAC5B,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,YAAS,AAAD;IACjC,MAAM,EAAE,OAAO,EAAE,GAAG,6JAAA,CAAA,UAAK,CAAC,GAAG,CAAC;IAE9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB,CAAC;QAC1B,uCAAuC;QACvC,aAAa,CAAA,YAAa,CAAC;gBACzB,GAAG,SAAS;gBACZ,QAAQ;YACV,CAAC;IACH;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,eAAe,UAAU,OAAO;gBAC9B,IAAI;oBACF,MAAM,QAAQ,MAAM,aAAa;oBACjC,QAAQ,GAAG,CAAC;oBAEZ,aAAa;gBACf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC/C;YACF;YACA,UAAU;QACZ;0BAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC,gLAAA,CAAA,UAAO;YAAC,SAAQ;sBACf,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAwB;;;;;;;;;;;;;;;;IAI/C;IAEA,qBACE,6LAAC;;0BACC,6LAAC,gLAAA,CAAA,UAAO;gBAAC,SAAQ;0BACf,cAAA,6LAAC,4IAAA,CAAA,UAAS;oBAAC,OAAO;;;;;;;;;;;0BAEpB,6LAAC,+IAAA,CAAA,UAAgB;gBACf,eAAe,UAAU,MAAM;gBAC/B,cAAc,UAAU,aAAa;gBACrC,SAAS,UAAU,EAAE;gBACrB,gBAAgB;;;;;;;;;;;;AAIxB;GAnDwB;;QACG,oIAAA,CAAA,YAAS;;;KADZ", "debugId": null}}]}