'use client'

import Link from "next/link"
import getStatusConfig from "@/utils/statusConfig";
import { cn } from "@/lib/utils";

export default function OrderReviewCard({ order }) {
    const statusConfig = getStatusConfig(order.status)
    const StatusIcon = statusConfig.icon

    return (
        <div className="bg-gray-50 rounded-xl p-4 mt-4 border border-gray-200 relative">
            <Link href={`/orders/${order.id}`} className="block hover:bg-gray-100 transition-colors rounded-xl p-2 -m-2">
                <div className="flex items-center justify-between mb-3">
                    <h5 className="text-base font-semibold text-gray-900">
                        Заказ #{order.id}
                    </h5>
                    <div className={cn(
                        "inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium border",
                        statusConfig.className
                    )}>
                        <StatusIcon className="w-3 h-3" />
                        {statusConfig.label}
                    </div>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                    <span className="text-sm text-gray-500 font-normal">{order.delivery_type}</span>
                    <div className="flex items-center justify-between">
                        <span>Гость: {order.user.first_name}</span>
                        <span className="font-medium text-gray-900">{order.amount} ₽</span>
                    </div>
                </div>
            </Link>
        </div>
    )
}