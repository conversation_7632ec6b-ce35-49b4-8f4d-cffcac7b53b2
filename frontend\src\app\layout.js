import { Ba<PERSON>_Fat_One, Mont<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import { MainProvider } from "@/contexts/MainProvider";

const bagelFatOne = Bagel_Fat_One({
  variable: "--font-bagel-fat-one",
  weight: "400",
  subsets: ["latin"],
});

const montserrat = Montserrat({
  variable: "--font-montserrat",
  subsets: ["latin", "cyrillic"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata = {
  title: "VKUS",
  description: "VKUS application",
};

export default function RootLayout({ children }) {
  return (
    <html lang="ru">
      <body
        className={`${bagelFatOne.variable} ${montserrat.variable} antialiased`}
      >
        <MainProvider>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
          </div>
        </MainProvider>
      </body>
    </html>
  );
}
