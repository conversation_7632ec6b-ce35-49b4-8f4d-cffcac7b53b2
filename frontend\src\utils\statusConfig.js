import { <PERSON><PERSON><PERSON>t, CheckCircle, Truck, Package, Clock, HandHeart, CheckChe<PERSON> } from "lucide-react";
import { ORDER_STATUS } from '@/constants/orderStatus';

export default function getStatusConfig(status) {
    const configs = {
        [ORDER_STATUS.NEW]: {
            icon: Clock,
            label: "Новый",
            buttonLabel: "Подтвердить",
            className: "bg-orange-50 text-orange-600 border-orange-200"
        },
        [ORDER_STATUS.CONFIRMED]: {
            icon: CheckCircle,
            label: "Подтвержден",
            buttonLabel: "Готов",
            className: "bg-blue-50 text-blue-600 border-blue-200"
        },
        [ORDER_STATUS.COMPLETED]: {
            icon: Package,
            label: "Готов",
            buttonLabel: "Отдать",
            className: "bg-green-50 text-green-600 border-green-200"
        },
        [ORDER_STATUS.GIVEN]: {
            icon: HandHeart,
            label: "Отдан",
            buttonLabel: "Отдан",
            className: "bg-emerald-50 text-emerald-600 border-emerald-200"
        },
        [ORDER_STATUS.DELIVERY]: {
            icon: Truck,
            label: "В пути",
            buttonLabel: "Доставлен",
            className: "bg-purple-50 text-purple-600 border-purple-200"
        },
        [ORDER_STATUS.DELIVERED]: {
            icon: CheckCheck,
            label: "Доставлен",
            buttonLabel: "Доставлен",
            className: "bg-indigo-50 text-indigo-600 border-indigo-200"
        },
        [ORDER_STATUS.CANCELLED]: {
            icon: TriangleAlert,
            label: "Отменен",
            buttonLabel: "Отменен",
            className: "bg-red-50 text-red-600 border-red-200"
        }
    };
    return configs[status] || configs[ORDER_STATUS.NEW];
}

/**
 * Получает конфигурацию для кнопки перехода к следующему статусу
 * @param {string} nextStatus - Следующий статус
 * @returns {object} - Конфигурация кнопки
 */
export function getNextStatusButtonConfig(nextStatus) {
    const buttonConfigs = {
        [ORDER_STATUS.CONFIRMED]: {
            icon: CheckCircle,
            label: "Подтвердить",
            className: "bg-blue-500 text-white hover:bg-blue-600"
        },
        [ORDER_STATUS.COMPLETED]: {
            icon: Package,
            label: "Готов",
            className: "bg-green-500 text-white hover:bg-green-600"
        },
        [ORDER_STATUS.GIVEN]: {
            icon: HandHeart,
            label: "Отдать",
            className: "bg-emerald-500 text-white hover:bg-emerald-600"
        },
        [ORDER_STATUS.DELIVERY]: {
            icon: Truck,
            label: "В доставку",
            className: "bg-purple-500 text-white hover:bg-purple-600"
        },
        [ORDER_STATUS.DELIVERED]: {
            icon: CheckCheck,
            label: "Доставлен",
            className: "bg-indigo-500 text-white hover:bg-indigo-600"
        }
    };

    return buttonConfigs[nextStatus] || {
        icon: CheckCircle,
        label: "Далее",
        className: "bg-gray-500 text-white hover:bg-gray-600"
    };
}