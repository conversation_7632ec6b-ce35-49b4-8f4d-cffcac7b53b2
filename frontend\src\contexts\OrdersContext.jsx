'use client'

import { createContext, useState, useContext, useEffect } from "react";
import OrdersService from "@/services/orders.service";

export const OrdersContext = createContext();

export const OrdersProvider = ({ children }) => {
    const [orders, setOrders] = useState([])
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(null)

    // Загружаем все заказы при инициализации
    useEffect(() => {
        loadOrders()
    }, [])

    async function loadOrders() {
        setLoading(true)
        setError(null)
        try {
            const ordersData = await OrdersService.getOrders()
            setOrders(ordersData)
        } catch (error) {
            console.error('Ошибка при загрузке заказов:', error)
            setError(error.message)
        } finally {
            setLoading(false)
        }
    }

    async function getOrders() {
        try {
            return await OrdersService.getOrders()
        } catch (error) {
            console.error('Ошибка при получении заказов:', error)
            setError(error.message)
            return []
        }
    }

    async function getOrdersByStatus(status) {
        try {
            return await OrdersService.getOrdersByStatus(status)
        } catch (error) {
            console.error('Ошибка при получении заказов по статусу:', error)
            setError(error.message)
            return []
        }
    }

    // Синхронная функция для получения заказов по статусу из уже загруженных данных
    function getOrdersByStatusSync(status) {
        return orders.filter(order => order.status === status)
    }

    // Функция для обновления статуса заказа
    async function updateOrderStatus(orderId, newStatus) {
        setLoading(true)
        setError(null)

        // Сохраняем текущее состояние для возможного отката
        const previousOrders = [...orders]

        try {
            // Сначала обновляем локальное состояние для быстрого отклика UI
            setOrders(prevOrders =>
                prevOrders.map(order =>
                    order.id === orderId
                        ? { ...order, status: newStatus }
                        : order
                )
            )

            // Отправляем запрос на сервер
            await OrdersService.updateOrderStatus(orderId, newStatus)

            return true
        } catch (error) {
            console.error('Ошибка при обновлении статуса заказа:', error)
            setError(error.message)

            // Откатываем изменения при ошибке
            setOrders(previousOrders)

            return false
        } finally {
            setLoading(false)
        }
    }

    async function getOrderById(id) {
        try {
            return await OrdersService.getOrderById(id)
        } catch (error) {
            console.error('Ошибка при получении заказа по ID:', error)
            setError(error.message)
            return null
        }
    }

    return (
        <OrdersContext.Provider value={{
            orders,
            setOrders,
            loading,
            error,
            getOrders,
            getOrdersByStatus,
            getOrdersByStatusSync,
            updateOrderStatus,
            getOrderById,
            loadOrders
        }}>
            {children}
        </OrdersContext.Provider>
    );
};

export function useOrders() {
    return useContext(OrdersContext)
}