'use client'

import React, { useEffect, useState } from "react";
import { Section } from "@/components/layout";
import OrderCard from "@/components/shared/OrderCard";
import NextStatusButton from "@/components/ui/NextStatusButton";
import { useOrders } from "@/contexts/OrdersContext";

export default function Order({ params }) {
  const { getOrderById } = useOrders()
  const { orderId } = React.use(params)

  const [orderData, setOrderData] = useState(null)

  const handleStatusUpdate = (newStatus) => {
    // Обновляем локальное состояние заказа
    setOrderData(prevOrder => ({
      ...prevOrder,
      status: newStatus
    }))
  }

  useEffect(() => {
    async function loadOrder(orderId) {
      try {
        const order = await getOrderById(orderId)
        console.log(order);
        
        setOrderData(order)
      } catch (error) {
        console.error('Ошибка при загрузке заказа:', error)
      }
    }
    loadOrder(orderId)
  }, [orderId])

  if (!orderData) {
    return (
      <Section spacing="xs">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-gray-600">Загрузка заказа...</div>
        </div>
      </Section>
    )
  }

  return (
    <main>
      <Section spacing="xs">
        <OrderCard order={orderData} />
      </Section>
      <NextStatusButton
        currentStatus={orderData.status}
        deliveryType={orderData.delivery_type}
        orderId={orderData.id}
        onStatusUpdate={handleStatusUpdate}
      />
    </main>
  );
}
