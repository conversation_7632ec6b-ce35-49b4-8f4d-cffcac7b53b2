(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/constants/orderStatus.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Enum для статусов заказов
 * Содержит все возможные статусы заказов и их строковые значения
 */ __turbopack_context__.s({
    "DELIVERY_STATUS_ORDER": ()=>DELIVERY_STATUS_ORDER,
    "HIDDEN_STATUSES": ()=>HIDDEN_STATUSES,
    "ORDER_STATUS": ()=>ORDER_STATUS,
    "PICKUP_STATUS_ORDER": ()=>PICKUP_STATUS_ORDER,
    "STATUS_ORDER": ()=>STATUS_ORDER,
    "getAllStatuses": ()=>getAllStatuses,
    "isValidStatus": ()=>isValidStatus
});
const ORDER_STATUS = {
    NEW: 'new',
    CONFIRMED: 'confirmed',
    COMPLETED: 'completed',
    GIVEN: 'given',
    DELIVERY: 'delivery',
    DELIVERED: 'delivered',
    CANCELLED: 'cancelled'
};
const STATUS_ORDER = [
    ORDER_STATUS.NEW,
    ORDER_STATUS.CONFIRMED,
    ORDER_STATUS.COMPLETED,
    ORDER_STATUS.DELIVERY,
    ORDER_STATUS.CANCELLED
];
const PICKUP_STATUS_ORDER = [
    ORDER_STATUS.NEW,
    ORDER_STATUS.CONFIRMED,
    ORDER_STATUS.COMPLETED,
    ORDER_STATUS.GIVEN
];
const DELIVERY_STATUS_ORDER = [
    ORDER_STATUS.NEW,
    ORDER_STATUS.CONFIRMED,
    ORDER_STATUS.COMPLETED,
    ORDER_STATUS.DELIVERY,
    ORDER_STATUS.DELIVERED
];
const HIDDEN_STATUSES = [
    ORDER_STATUS.GIVEN,
    ORDER_STATUS.DELIVERED
];
const isValidStatus = (status)=>{
    return Object.values(ORDER_STATUS).includes(status);
};
const getAllStatuses = ()=>{
    return Object.values(ORDER_STATUS);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/getNextStatus.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>getNextStatus
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/orderStatus.js [app-client] (ecmascript)");
;
function getNextStatus(currentStatus, deliveryType) {
    const isPickup = deliveryType === "Самовывоз";
    const statusOrder = isPickup ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PICKUP_STATUS_ORDER"] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DELIVERY_STATUS_ORDER"];
    const currentIndex = statusOrder.indexOf(currentStatus);
    // Если статус не найден или это последний статус в цепочке
    if (currentIndex === -1 || currentIndex >= statusOrder.length - 1) {
        return null;
    }
    return statusOrder[currentIndex + 1];
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/statusConfig.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>getStatusConfig,
    "getNextStatusButtonConfig": ()=>getNextStatusButtonConfig
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TriangleAlert$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as TriangleAlert>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/truck.js [app-client] (ecmascript) <export default as Truck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/package.js [app-client] (ecmascript) <export default as Package>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hand$2d$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HandHeart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/hand-heart.js [app-client] (ecmascript) <export default as HandHeart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/check-check.js [app-client] (ecmascript) <export default as CheckCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/orderStatus.js [app-client] (ecmascript)");
;
;
function getStatusConfig(status) {
    const configs = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].NEW]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"],
            label: "Новый",
            buttonLabel: "Подтвердить",
            className: "bg-orange-50 text-orange-600 border-orange-200"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].CONFIRMED]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"],
            label: "Подтвержден",
            buttonLabel: "Готов",
            className: "bg-blue-50 text-blue-600 border-blue-200"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].COMPLETED]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"],
            label: "Готов",
            buttonLabel: "Отдать",
            className: "bg-green-50 text-green-600 border-green-200"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].GIVEN]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hand$2d$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HandHeart$3e$__["HandHeart"],
            label: "Отдан",
            buttonLabel: "Отдан",
            className: "bg-emerald-50 text-emerald-600 border-emerald-200"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].DELIVERY]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__["Truck"],
            label: "В пути",
            buttonLabel: "Доставлен",
            className: "bg-purple-50 text-purple-600 border-purple-200"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].DELIVERED]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__["CheckCheck"],
            label: "Доставлен",
            buttonLabel: "Доставлен",
            className: "bg-indigo-50 text-indigo-600 border-indigo-200"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].CANCELLED]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TriangleAlert$3e$__["TriangleAlert"],
            label: "Отменен",
            buttonLabel: "Отменен",
            className: "bg-red-50 text-red-600 border-red-200"
        }
    };
    return configs[status] || configs[__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].NEW];
}
function getNextStatusButtonConfig(nextStatus) {
    const buttonConfigs = {
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].CONFIRMED]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"],
            label: "Подтвердить",
            className: "bg-blue-500 text-white hover:bg-blue-600"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].COMPLETED]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"],
            label: "Готов",
            className: "bg-green-500 text-white hover:bg-green-600"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].GIVEN]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$hand$2d$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__HandHeart$3e$__["HandHeart"],
            label: "Отдать",
            className: "bg-emerald-500 text-white hover:bg-emerald-600"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].DELIVERY]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$truck$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Truck$3e$__["Truck"],
            label: "В доставку",
            className: "bg-purple-500 text-white hover:bg-purple-600"
        },
        [__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].DELIVERED]: {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$check$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCheck$3e$__["CheckCheck"],
            label: "Доставлен",
            className: "bg-indigo-500 text-white hover:bg-indigo-600"
        }
    };
    return buttonConfigs[nextStatus] || {
        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"],
        label: "Далее",
        className: "bg-gray-500 text-white hover:bg-gray-600"
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": ()=>cn
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn() {
    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){
        inputs[_key] = arguments[_key];
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/NextStatusButton.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>NextStatusButton
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$getNextStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/getNextStatus.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$statusConfig$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/statusConfig.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrdersContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/OrdersContext.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-client] (ecmascript) <export default as ChevronRight>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function NextStatusButton(param) {
    let { currentStatus, deliveryType, orderId, onStatusUpdate } = param;
    _s();
    const { updateOrderStatus } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrdersContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOrders"])();
    const [nextStatus, setNextStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isUpdating, setIsUpdating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Состояния для свайп-жеста
    const [swipeProgress, setSwipeProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [isSwipeActive, setIsSwipeActive] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isSwipeCompleted, setIsSwipeCompleted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const buttonRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const startXRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const currentXRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NextStatusButton.useEffect": ()=>{
            setNextStatus((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$getNextStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(currentStatus, deliveryType));
        }
    }["NextStatusButton.useEffect"], [
        currentStatus,
        deliveryType
    ]);
    // Автоматически скрываем ошибку через 5 секунд
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NextStatusButton.useEffect": ()=>{
            if (error) {
                const timer = setTimeout({
                    "NextStatusButton.useEffect.timer": ()=>{
                        setError(null);
                    }
                }["NextStatusButton.useEffect.timer"], 5000);
                return ({
                    "NextStatusButton.useEffect": ()=>clearTimeout(timer)
                })["NextStatusButton.useEffect"];
            }
        }
    }["NextStatusButton.useEffect"], [
        error
    ]);
    // Обработчики свайп-жестов
    const handleSwipeStart = (clientX)=>{
        if (isUpdating) return;
        setIsSwipeActive(true);
        setIsSwipeCompleted(false);
        setSwipeProgress(0);
        startXRef.current = clientX;
        currentXRef.current = clientX;
    };
    const handleSwipeMove = (clientX)=>{
        var _buttonRef_current;
        if (!isSwipeActive || isUpdating) return;
        currentXRef.current = clientX;
        const deltaX = clientX - startXRef.current;
        const buttonWidth = ((_buttonRef_current = buttonRef.current) === null || _buttonRef_current === void 0 ? void 0 : _buttonRef_current.offsetWidth) || 0;
        // Более чувствительный расчет прогресса
        const progress = Math.max(0, Math.min(1, deltaX / (buttonWidth * 0.6))) // 60% для активации
        ;
        setSwipeProgress(progress);
        if (progress >= 1 && !isSwipeCompleted) {
            setIsSwipeCompleted(true);
            // Тактильная обратная связь на мобильных устройствах
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
            // Добавляем небольшую задержку для лучшего UX
            setTimeout(()=>{
                handleSwipeComplete();
            }, 150);
        }
    };
    const handleSwipeEnd = ()=>{
        if (!isSwipeCompleted) {
            // Плавная анимация возврата в исходное положение
            const resetProgress = ()=>{
                setSwipeProgress((prev)=>{
                    const newProgress = prev * 0.8;
                    if (newProgress > 0.01) {
                        requestAnimationFrame(resetProgress);
                        return newProgress;
                    }
                    return 0;
                });
            };
            resetProgress();
        }
        setIsSwipeActive(false);
    };
    // Обработчики для мыши
    const handleMouseDown = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        handleSwipeStart(e.clientX);
        const handleMouseMove = (e)=>{
            e.preventDefault();
            handleSwipeMove(e.clientX);
        };
        const handleMouseUp = (e)=>{
            e.preventDefault();
            handleSwipeEnd();
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
        document.addEventListener('mousemove', handleMouseMove, {
            passive: false
        });
        document.addEventListener('mouseup', handleMouseUp, {
            passive: false
        });
    };
    // Обработчики для touch
    const handleTouchStart = (e)=>{
        e.stopPropagation();
        handleSwipeStart(e.touches[0].clientX);
    };
    const handleTouchMove = (e)=>{
        e.preventDefault();
        e.stopPropagation();
        handleSwipeMove(e.touches[0].clientX);
    };
    const handleTouchEnd = (e)=>{
        e.stopPropagation();
        handleSwipeEnd();
    };
    const handleSwipeComplete = async ()=>{
        if (!nextStatus || isUpdating) return;
        // Выполняем действие обновления статуса
        await handleStatusUpdate();
        // Сбрасываем состояние свайпа
        setTimeout(()=>{
            setSwipeProgress(0);
            setIsSwipeCompleted(false);
        }, 300);
    };
    const buttonConfig = nextStatus ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$statusConfig$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNextStatusButtonConfig"])(nextStatus) : null;
    const StatusIcon = buttonConfig === null || buttonConfig === void 0 ? void 0 : buttonConfig.icon;
    // 🎨 Функция для определения цветовой схемы на основе статуса
    const getStatusColorScheme = ()=>{
        if (!buttonConfig) return "bg-gradient-to-r from-gray-500 to-gray-600 text-white";
        // Определяем цвет на основе следующего статуса
        if (nextStatus === null || nextStatus === void 0 ? void 0 : nextStatus.includes('confirmed')) {
            return "bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700";
        } else if (nextStatus === null || nextStatus === void 0 ? void 0 : nextStatus.includes('completed')) {
            return "bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700";
        } else if (nextStatus === null || nextStatus === void 0 ? void 0 : nextStatus.includes('given')) {
            return "bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700";
        } else if (nextStatus === null || nextStatus === void 0 ? void 0 : nextStatus.includes('delivery')) {
            return "bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:from-purple-600 hover:to-purple-700";
        } else if (nextStatus === null || nextStatus === void 0 ? void 0 : nextStatus.includes('delivered')) {
            return "bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:from-indigo-600 hover:to-indigo-700";
        } else {
            return "bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700";
        }
    };
    const handleStatusUpdate = async ()=>{
        if (!nextStatus || isUpdating) return;
        setIsUpdating(true);
        setError(null);
        try {
            const success = await updateOrderStatus(orderId, nextStatus);
            if (success) {
                // Уведомляем родительский компонент об успешном обновлении
                if (onStatusUpdate) {
                    onStatusUpdate(nextStatus);
                }
            } else {
                setError('Не удалось обновить статус заказа');
            }
        } catch (error) {
            console.error('Ошибка при обновлении статуса:', error);
            setError(error.message || 'Произошла ошибка при обновлении статуса');
        } finally{
            setIsUpdating(false);
        }
    };
    if (!nextStatus || !buttonConfig) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "sticky bottom-0 w-full bg-white/80 backdrop-blur-md border-t border-gray-200/50",
        children: [
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mx-4 mt-4 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-lg shadow-sm",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-5 h-5 bg-red-500 rounded-full flex items-center justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-white text-xs font-bold",
                                children: "!"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                lineNumber: 202,
                                columnNumber: 29
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                            lineNumber: 201,
                            columnNumber: 25
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-red-700 font-medium",
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                            lineNumber: 204,
                            columnNumber: 25
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                    lineNumber: 200,
                    columnNumber: 21
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                lineNumber: 199,
                columnNumber: 17
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative overflow-hidden rounded-xl shadow-lg",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        ref: buttonRef,
                        disabled: isUpdating,
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("relative w-full h-20 font-semibold px-6 py-4 transition-all duration-500 ease-out", "select-none touch-none rounded-xl border-0 outline-none focus:outline-none", "backdrop-blur-sm", isUpdating ? "opacity-60 cursor-not-allowed" : "cursor-grab active:cursor-grabbing", isSwipeActive ? "scale-[1.01] shadow-2xl" : "shadow-lg hover:shadow-xl", isSwipeCompleted ? "scale-[1.02] shadow-2xl" : "", getStatusColorScheme()),
                        onMouseDown: handleMouseDown,
                        onTouchStart: handleTouchStart,
                        onTouchMove: handleTouchMove,
                        onTouchEnd: handleTouchEnd,
                        style: {
                            WebkitTouchCallout: 'none',
                            WebkitUserSelect: 'none',
                            touchAction: 'none'
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-0 rounded-xl transition-all duration-300 ease-out",
                                style: {
                                    background: "linear-gradient(90deg,\n                                    rgba(255,255,255,0.25) 0%,\n                                    rgba(255,255,255,0.15) 50%,\n                                    rgba(255,255,255,0.05) 100%)",
                                    width: "".concat(swipeProgress * 100, "%"),
                                    opacity: isSwipeActive ? 1 : 0,
                                    boxShadow: isSwipeActive ? 'inset 0 0 20px rgba(255,255,255,0.2)' : 'none'
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                lineNumber: 234,
                                columnNumber: 25
                            }, this),
                            isSwipeActive && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute top-0 h-full transition-all duration-200 ease-out",
                                style: {
                                    right: "".concat((1 - swipeProgress) * 100, "%"),
                                    width: '4px',
                                    background: 'linear-gradient(180deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%)',
                                    borderRadius: '0 2px 2px 0',
                                    opacity: swipeProgress > 0.1 ? 1 : 0,
                                    boxShadow: '0 0 10px rgba(255,255,255,0.5)'
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                lineNumber: 249,
                                columnNumber: 29
                            }, this),
                            isSwipeCompleted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-0 rounded-xl bg-white/20 animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                lineNumber: 264,
                                columnNumber: 29
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative z-10 flex items-center justify-between h-full",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-4",
                                        children: [
                                            isUpdating ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-6 h-6 border-3 border-white/30 border-t-white rounded-full animate-spin"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                lineNumber: 271,
                                                columnNumber: 37
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "relative",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatusIcon, {
                                                        className: "w-6 h-6 drop-shadow-sm"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                        lineNumber: 274,
                                                        columnNumber: 41
                                                    }, this),
                                                    isSwipeCompleted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full flex items-center justify-center",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "w-1.5 h-1.5 bg-green-500 rounded-full"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                            lineNumber: 277,
                                                            columnNumber: 49
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                        lineNumber: 276,
                                                        columnNumber: 45
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                lineNumber: 273,
                                                columnNumber: 37
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex flex-col justify-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-lg font-light tracking-wide drop-shadow-sm",
                                                        children: isUpdating ? "Обновление..." : isSwipeCompleted ? "Выполнено!" : isSwipeActive ? "".concat(Math.round(swipeProgress * 100), "%") : buttonConfig.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                        lineNumber: 283,
                                                        columnNumber: 37
                                                    }, this),
                                                    !isUpdating && !isSwipeCompleted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm opacity-90 font-medium tracking-wide",
                                                        children: isSwipeActive ? "Продолжайте..." : "Проведите для подтверждения"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                        lineNumber: 290,
                                                        columnNumber: 41
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                lineNumber: 282,
                                                columnNumber: 33
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                        lineNumber: 269,
                                        columnNumber: 29
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: isSwipeCompleted ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-8 h-8 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-white text-lg font-bold",
                                                children: "✓"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                lineNumber: 301,
                                                columnNumber: 41
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                            lineNumber: 300,
                                            columnNumber: 37
                                        }, this) : !isUpdating && !isSwipeActive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-1 opacity-70",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center animate-pulse",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                                        className: "w-5 h-5 drop-shadow-sm"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                        lineNumber: 306,
                                                        columnNumber: 45
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                                        className: "w-5 h-5 -ml-2 drop-shadow-sm"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                        lineNumber: 307,
                                                        columnNumber: 45
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                                        className: "w-5 h-5 -ml-2 drop-shadow-sm"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                        lineNumber: 308,
                                                        columnNumber: 45
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                lineNumber: 305,
                                                columnNumber: 41
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                            lineNumber: 304,
                                            columnNumber: 37
                                        }, this) : isSwipeActive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-white text-lg font-bold drop-shadow-sm",
                                                    children: swipeProgress >= 0.8 ? "Отпустите!" : "→"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                    lineNumber: 313,
                                                    columnNumber: 41
                                                }, this),
                                                swipeProgress >= 0.8 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-2 h-2 bg-white rounded-full animate-ping"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                                    lineNumber: 317,
                                                    columnNumber: 45
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                            lineNumber: 312,
                                            columnNumber: 37
                                        }, this) : null
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                        lineNumber: 298,
                                        columnNumber: 29
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                                lineNumber: 268,
                                columnNumber: 25
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                        lineNumber: 211,
                        columnNumber: 21
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                    lineNumber: 210,
                    columnNumber: 17
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/NextStatusButton.jsx",
                lineNumber: 209,
                columnNumber: 13
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/NextStatusButton.jsx",
        lineNumber: 197,
        columnNumber: 9
    }, this);
}
_s(NextStatusButton, "E/Dg1sSgzyTZRKkXMuf2ImywxBU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrdersContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOrders"]
    ];
});
_c = NextStatusButton;
var _c;
__turbopack_context__.k.register(_c, "NextStatusButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/demo/page.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>DemoPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$NextStatusButton$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/NextStatusButton.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/orderStatus.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrdersContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/OrdersContext.jsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function DemoPage() {
    _s();
    const [currentStatus, setCurrentStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].NEW);
    const [deliveryType, setDeliveryType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('Самовывоз');
    const handleStatusUpdate = (newStatus)=>{
        setCurrentStatus(newStatus);
        console.log('Статус обновлен на:', newStatus);
    };
    const resetToNew = ()=>{
        setCurrentStatus(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$orderStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ORDER_STATUS"].NEW);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$OrdersContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OrdersProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md mx-auto bg-white/80 backdrop-blur-md rounded-2xl shadow-2xl overflow-hidden border border-white/20",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-2xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",
                                    children: "🎨 Интерактивная кнопка статуса"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/page.jsx",
                                    lineNumber: 26,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200/50 rounded-xl p-5 mb-6 shadow-sm",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "font-bold text-blue-800 mb-3 flex items-center gap-2",
                                            children: "✨ Интерактивное управление статусами"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 31,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-blue-700 space-y-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs",
                                                            children: "📱"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 36,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Мобильный:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                                    lineNumber: 37,
                                                                    columnNumber: 43
                                                                }, this),
                                                                " проведите пальцем слева направо"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 37,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                    lineNumber: 35,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs",
                                                            children: "🖱️"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 40,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Десктоп:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                                    lineNumber: 41,
                                                                    columnNumber: 43
                                                                }, this),
                                                                " зажмите и тяните мышью"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 41,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                    lineNumber: 39,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs",
                                                            children: "⚡"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 44,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Активация:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                                    lineNumber: 45,
                                                                    columnNumber: 43
                                                                }, this),
                                                                " при достижении ~60% ширины"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 45,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                    lineNumber: 43,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs",
                                                            children: "📳"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 48,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                    children: "Обратная связь:"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                                    lineNumber: 49,
                                                                    columnNumber: 43
                                                                }, this),
                                                                " вибрация и анимации"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 49,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                    lineNumber: 47,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 34,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/demo/page.jsx",
                                    lineNumber: 30,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4 mb-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "block text-sm font-medium text-gray-700 mb-2",
                                                    children: "Тип доставки:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                    lineNumber: 56,
                                                    columnNumber: 33
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                    value: deliveryType,
                                                    onChange: (e)=>setDeliveryType(e.target.value),
                                                    className: "w-full p-2 border border-gray-300 rounded-md",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                            value: "Самовывоз",
                                                            children: "Самовывоз"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 64,
                                                            columnNumber: 37
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                            value: "Доставка",
                                                            children: "Доставка"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/demo/page.jsx",
                                                            lineNumber: 65,
                                                            columnNumber: 37
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                    lineNumber: 59,
                                                    columnNumber: 33
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 55,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: resetToNew,
                                            className: "w-full p-2 bg-gray-500 text-white rounded-md hover:bg-gray-600",
                                            children: 'Сбросить к статусу "Новый"'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 69,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/demo/page.jsx",
                                    lineNumber: 54,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gradient-to-r from-gray-50 to-gray-100 p-5 rounded-xl mb-6 border border-gray-200/50 shadow-sm",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium text-gray-600",
                                                        children: "Текущий статус:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/demo/page.jsx",
                                                        lineNumber: 80,
                                                        columnNumber: 37
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold",
                                                        children: currentStatus
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/demo/page.jsx",
                                                        lineNumber: 81,
                                                        columnNumber: 37
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/demo/page.jsx",
                                                lineNumber: 79,
                                                columnNumber: 33
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-sm font-medium text-gray-600",
                                                        children: "Тип доставки:"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/demo/page.jsx",
                                                        lineNumber: 86,
                                                        columnNumber: 37
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-semibold",
                                                        children: deliveryType
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/demo/page.jsx",
                                                        lineNumber: 87,
                                                        columnNumber: 37
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/demo/page.jsx",
                                                lineNumber: 85,
                                                columnNumber: 33
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/demo/page.jsx",
                                        lineNumber: 78,
                                        columnNumber: 29
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/page.jsx",
                                    lineNumber: 77,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-gray-500 mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                children: "Логика переходов:"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/demo/page.jsx",
                                                lineNumber: 95,
                                                columnNumber: 32
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 95,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                    children: "Самовывоз:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                    lineNumber: 96,
                                                    columnNumber: 32
                                                }, this),
                                                " Новый → Подтвержден → Готов → Отдан"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 96,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                    children: "Доставка:"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/page.jsx",
                                                    lineNumber: 97,
                                                    columnNumber: 32
                                                }, this),
                                                " Новый → Подтвержден → Готов → В пути → Доставлен"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 97,
                                            columnNumber: 29
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/demo/page.jsx",
                                    lineNumber: 94,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/demo/page.jsx",
                            lineNumber: 25,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$NextStatusButton$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            currentStatus: currentStatus,
                            deliveryType: deliveryType,
                            orderId: "demo-order",
                            onStatusUpdate: handleStatusUpdate
                        }, void 0, false, {
                            fileName: "[project]/src/app/demo/page.jsx",
                            lineNumber: 101,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/demo/page.jsx",
                    lineNumber: 24,
                    columnNumber: 17
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md mx-auto mt-8 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200/50 rounded-xl p-5 shadow-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "font-bold text-yellow-800 mb-3 flex items-center gap-2",
                            children: "⚙️ Настройки эксперимента"
                        }, void 0, false, {
                            fileName: "[project]/src/app/demo/page.jsx",
                            lineNumber: 110,
                            columnNumber: 21
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-2 text-sm text-yellow-700",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "w-2 h-2 bg-yellow-500 rounded-full"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 115,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                            children: "Отключить свайп:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 116,
                                            columnNumber: 29
                                        }, this),
                                        " ",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("code", {
                                            className: "bg-yellow-100 px-2 py-1 rounded",
                                            children: "ENABLE_SWIPE_GESTURE = false"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 116,
                                            columnNumber: 63
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/demo/page.jsx",
                                    lineNumber: 114,
                                    columnNumber: 25
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "w-2 h-2 bg-orange-500 rounded-full"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 119,
                                            columnNumber: 29
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                            children: "Файл:"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 120,
                                            columnNumber: 29
                                        }, this),
                                        " ",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("code", {
                                            className: "bg-yellow-100 px-2 py-1 rounded",
                                            children: "NextStatusButton.jsx"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/page.jsx",
                                            lineNumber: 120,
                                            columnNumber: 52
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/demo/page.jsx",
                                    lineNumber: 118,
                                    columnNumber: 25
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/demo/page.jsx",
                            lineNumber: 113,
                            columnNumber: 21
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/demo/page.jsx",
                    lineNumber: 109,
                    columnNumber: 17
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/demo/page.jsx",
            lineNumber: 23,
            columnNumber: 13
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/demo/page.jsx",
        lineNumber: 22,
        columnNumber: 9
    }, this);
}
_s(DemoPage, "ERAlMhRJfO7pWp3PtfbUcc0ECRk=");
_c = DemoPage;
var _c;
__turbopack_context__.k.register(_c, "DemoPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_fac91fb5._.js.map