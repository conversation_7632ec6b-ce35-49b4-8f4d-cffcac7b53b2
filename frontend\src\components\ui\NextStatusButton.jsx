'use client'

import { useEffect, useState, useRef } from "react";
import getNextStatus from "@/utils/getNextStatus";
import { getNextStatusButtonConfig } from "@/utils/statusConfig";
import { cn } from "@/lib/utils";
import { useOrders } from "@/contexts/OrdersContext";
import { ChevronRight } from "lucide-react";

export default function NextStatusButton({ currentStatus, deliveryType, orderId, onStatusUpdate }) {
    const { updateOrderStatus } = useOrders()
    const [nextStatus, setNextStatus] = useState(null)
    const [isUpdating, setIsUpdating] = useState(false)
    const [error, setError] = useState(null)

    // Состояния для свайп-жеста
    const [swipeProgress, setSwipeProgress] = useState(0)
    const [isSwipeActive, setIsSwipeActive] = useState(false)
    const [isSwipeCompleted, setIsSwipeCompleted] = useState(false)
    const buttonRef = useRef(null)
    const startXRef = useRef(0)
    const currentXRef = useRef(0)

    useEffect(() => {
        setNextStatus(getNextStatus(currentStatus, deliveryType))
    }, [currentStatus, deliveryType])

    // Автоматически скрываем ошибку через 5 секунд
    useEffect(() => {
        if (error) {
            const timer = setTimeout(() => {
                setError(null)
            }, 5000)
            return () => clearTimeout(timer)
        }
    }, [error])

    // Обработчики свайп-жестов
    const handleSwipeStart = (clientX) => {
        if (isUpdating) return

        setIsSwipeActive(true)
        setIsSwipeCompleted(false)
        setSwipeProgress(0)
        startXRef.current = clientX
        currentXRef.current = clientX
    }

    const handleSwipeMove = (clientX) => {
        if (!isSwipeActive || isUpdating) return

        currentXRef.current = clientX
        const deltaX = clientX - startXRef.current
        const buttonWidth = buttonRef.current?.offsetWidth || 0

        // Более чувствительный расчет прогресса
        const progress = Math.max(0, Math.min(1, deltaX / (buttonWidth * 0.6))) // 60% для активации

        setSwipeProgress(progress)

        if (progress >= 1 && !isSwipeCompleted) {
            setIsSwipeCompleted(true)

            // Тактильная обратная связь на мобильных устройствах
            if (navigator.vibrate) {
                navigator.vibrate(50)
            }

            // Добавляем небольшую задержку для лучшего UX
            setTimeout(() => {
                handleSwipeComplete()
            }, 150)
        }
    }

    const handleSwipeEnd = () => {
        if (!isSwipeCompleted) {
            // Плавная анимация возврата в исходное положение
            const resetProgress = () => {
                setSwipeProgress(prev => {
                    const newProgress = prev * 0.8
                    if (newProgress > 0.01) {
                        requestAnimationFrame(resetProgress)
                        return newProgress
                    }
                    return 0
                })
            }
            resetProgress()
        }
        setIsSwipeActive(false)
    }

    // Обработчики для мыши
    const handleMouseDown = (e) => {
        e.preventDefault()
        e.stopPropagation()
        handleSwipeStart(e.clientX)

        const handleMouseMove = (e) => {
            e.preventDefault()
            handleSwipeMove(e.clientX)
        }
        const handleMouseUp = (e) => {
            e.preventDefault()
            handleSwipeEnd()
            document.removeEventListener('mousemove', handleMouseMove)
            document.removeEventListener('mouseup', handleMouseUp)
        }

        document.addEventListener('mousemove', handleMouseMove, { passive: false })
        document.addEventListener('mouseup', handleMouseUp, { passive: false })
    }

    // Обработчики для touch
    const handleTouchStart = (e) => {
        e.stopPropagation()
        handleSwipeStart(e.touches[0].clientX)
    }

    const handleTouchMove = (e) => {
        e.preventDefault()
        e.stopPropagation()
        handleSwipeMove(e.touches[0].clientX)
    }

    const handleTouchEnd = (e) => {
        e.stopPropagation()
        handleSwipeEnd()
    }

    const handleSwipeComplete = async () => {
        if (!nextStatus || isUpdating) return

        // Выполняем действие обновления статуса
        await handleStatusUpdate()

        // Сбрасываем состояние свайпа
        setTimeout(() => {
            setSwipeProgress(0)
            setIsSwipeCompleted(false)
        }, 300)
    }

    const buttonConfig = nextStatus ? getNextStatusButtonConfig(nextStatus) : null
    const StatusIcon = buttonConfig?.icon

    // 🎨 Функция для определения цветовой схемы на основе статуса
    const getStatusColorScheme = () => {
        if (!buttonConfig) return "bg-gradient-to-r from-gray-500 to-gray-600 text-white"

        // Определяем цвет на основе следующего статуса
        if (nextStatus?.includes('confirmed')) {
            return "bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700"
        } else if (nextStatus?.includes('completed')) {
            return "bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700"
        } else if (nextStatus?.includes('given')) {
            return "bg-gradient-to-r from-emerald-500 to-emerald-600 text-white hover:from-emerald-600 hover:to-emerald-700"
        } else if (nextStatus?.includes('delivery')) {
            return "bg-gradient-to-r from-purple-500 to-purple-600 text-white hover:from-purple-600 hover:to-purple-700"
        } else if (nextStatus?.includes('delivered')) {
            return "bg-gradient-to-r from-indigo-500 to-indigo-600 text-white hover:from-indigo-600 hover:to-indigo-700"
        } else {
            return "bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700"
        }
    }

    const handleStatusUpdate = async () => {
        if (!nextStatus || isUpdating) return

        setIsUpdating(true)
        setError(null)

        try {
            const success = await updateOrderStatus(orderId, nextStatus)
            if (success) {
                // Уведомляем родительский компонент об успешном обновлении
                if (onStatusUpdate) {
                    onStatusUpdate(nextStatus)
                }
            } else {
                setError('Не удалось обновить статус заказа')
            }
        } catch (error) {
            console.error('Ошибка при обновлении статуса:', error)
            setError(error.message || 'Произошла ошибка при обновлении статуса')
        } finally {
            setIsUpdating(false)
        }
    }

    if (!nextStatus || !buttonConfig) {
        return null
    }

    return (
        <div className="sticky bottom-0 w-full bg-white/80 backdrop-blur-md border-t border-gray-200/50">
            {error && (
                <div className="mx-4 mt-4 p-4 bg-gradient-to-r from-red-50 to-red-100 border border-red-200 rounded-lg shadow-sm">
                    <div className="flex items-center gap-3">
                        <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-bold">!</span>
                        </div>
                        <span className="text-red-700 font-medium">{error}</span>
                    </div>
                </div>
            )}

            <div className="p-4">
                <div className="relative overflow-hidden rounded-xl shadow-lg">
                    <button
                        ref={buttonRef}
                        disabled={isUpdating}
                        className={cn(
                            "relative w-full h-20 font-semibold px-6 py-4 transition-all duration-500 ease-out",
                            "select-none touch-none rounded-xl border-0 outline-none focus:outline-none",
                            "backdrop-blur-sm",
                            isUpdating ? "opacity-60 cursor-not-allowed" : "cursor-grab active:cursor-grabbing",
                            isSwipeActive ? "scale-[1.01] shadow-2xl" : "shadow-lg hover:shadow-xl",
                            isSwipeCompleted ? "scale-[1.02] shadow-2xl" : "",
                            getStatusColorScheme()
                        )}
                        onMouseDown={handleMouseDown}
                        onTouchStart={handleTouchStart}
                        onTouchMove={handleTouchMove}
                        onTouchEnd={handleTouchEnd}
                        style={{
                            WebkitTouchCallout: 'none',
                            WebkitUserSelect: 'none',
                            touchAction: 'none'
                        }}
                    >
                        {/* Стильная фоновая заливка прогресса */}
                        <div
                            className="absolute inset-0 rounded-xl transition-all duration-300 ease-out"
                            style={{
                                background: `linear-gradient(90deg,
                                    rgba(255,255,255,0.25) 0%,
                                    rgba(255,255,255,0.15) 50%,
                                    rgba(255,255,255,0.05) 100%)`,
                                width: `${swipeProgress * 100}%`,
                                opacity: isSwipeActive ? 1 : 0,
                                boxShadow: isSwipeActive ? 'inset 0 0 20px rgba(255,255,255,0.2)' : 'none'
                            }}
                        />

                        {/* Анимированный индикатор прогресса */}
                        {isSwipeActive && (
                            <div
                                className="absolute top-0 h-full transition-all duration-200 ease-out"
                                style={{
                                    right: `${(1 - swipeProgress) * 100}%`,
                                    width: '4px',
                                    background: 'linear-gradient(180deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.4) 100%)',
                                    borderRadius: '0 2px 2px 0',
                                    opacity: swipeProgress > 0.1 ? 1 : 0,
                                    boxShadow: '0 0 10px rgba(255,255,255,0.5)'
                                }}
                            />
                        )}

                        {/* Эффект завершения */}
                        {isSwipeCompleted && (
                            <div className="absolute inset-0 rounded-xl bg-white/20 animate-pulse" />
                        )}

                        {/* Стильный основной контент */}
                        <div className="relative z-10 flex items-center justify-between h-full">
                            <div className="flex items-center gap-4">
                                {isUpdating ? (
                                    <div className="w-6 h-6 border-3 border-white/30 border-t-white rounded-full animate-spin" />
                                ) : (
                                    <div className="relative">
                                        <StatusIcon className="w-6 h-6 drop-shadow-sm" />
                                        {isSwipeCompleted && (
                                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full flex items-center justify-center">
                                                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                            </div>
                                        )}
                                    </div>
                                )}
                                <div className="flex flex-col justify-center">
                                    <span className="text-lg font-light tracking-wide drop-shadow-sm">
                                        {isUpdating ? "Обновление..." :
                                         isSwipeCompleted ? "Выполнено!" :
                                         isSwipeActive ? `${Math.round(swipeProgress * 100)}%` :
                                         buttonConfig.label}
                                    </span>
                                    {!isUpdating && !isSwipeCompleted && (
                                        <span className="text-sm opacity-90 font-medium tracking-wide">
                                            {isSwipeActive ? "Продолжайте..." : "Проведите для подтверждения"}
                                        </span>
                                    )}
                                </div>
                            </div>

                            {/* Улучшенные визуальные подсказки */}
                            <div className="flex items-center">
                                {isSwipeCompleted ? (
                                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                                        <div className="text-white text-lg font-bold">✓</div>
                                    </div>
                                ) : !isUpdating && !isSwipeActive ? (
                                    <div className="flex items-center gap-1 opacity-70">
                                        <div className="flex items-center animate-pulse">
                                            <ChevronRight className="w-5 h-5 drop-shadow-sm" />
                                            <ChevronRight className="w-5 h-5 -ml-2 drop-shadow-sm" />
                                            <ChevronRight className="w-5 h-5 -ml-2 drop-shadow-sm" />
                                        </div>
                                    </div>
                                ) : isSwipeActive ? (
                                    <div className="flex items-center gap-2">
                                        <div className="text-white text-lg font-bold drop-shadow-sm">
                                            {swipeProgress >= 0.8 ? "Отпустите!" : "→"}
                                        </div>
                                        {swipeProgress >= 0.8 && (
                                            <div className="w-2 h-2 bg-white rounded-full animate-ping"></div>
                                        )}
                                    </div>
                                ) : null}
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    )
}